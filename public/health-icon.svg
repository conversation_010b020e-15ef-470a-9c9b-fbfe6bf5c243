<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆形 -->
  <circle cx="32" cy="32" r="32" fill="url(#gradient)"/>
  
  <!-- 心形图标 -->
  <path d="M32 50C32 50 20 40 20 28C20 22 24 18 28 18C30 18 32 20 32 20C32 20 34 18 36 18C40 18 44 22 44 28C44 40 32 50 32 50Z" 
        fill="white" 
        stroke="none"/>
  
  <!-- 十字医疗标志 -->
  <rect x="30" y="24" width="4" height="16" fill="#3b82f6" rx="1"/>
  <rect x="24" y="30" width="16" height="4" fill="#3b82f6" rx="1"/>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>