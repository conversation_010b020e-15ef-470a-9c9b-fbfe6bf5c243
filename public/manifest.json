{"name": "个人健康助理", "short_name": "健康助理", "description": "智能解读体检报告，提供专业健康咨询", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#3b82f6", "orientation": "portrait-primary", "categories": ["health", "medical", "lifestyle"], "lang": "zh-CN", "scope": "/", "icons": [{"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "any maskable"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "any maskable"}, {"src": "/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "any maskable"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "any maskable"}, {"src": "/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "any maskable"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "any maskable"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}], "shortcuts": [{"name": "报告解读", "short_name": "报告解读", "description": "拍照或上传体检报告进行AI解读", "url": "/report", "icons": [{"src": "/icons/shortcut-report.png", "sizes": "96x96"}]}, {"name": "健康咨询", "short_name": "健康咨询", "description": "与AI健康顾问进行对话咨询", "url": "/consultation", "icons": [{"src": "/icons/shortcut-chat.png", "sizes": "96x96"}]}, {"name": "症状自评", "short_name": "症状自评", "description": "记录症状并获得智能评估", "url": "/assessment", "icons": [{"src": "/icons/shortcut-assess.png", "sizes": "96x96"}]}], "screenshots": [{"src": "/screenshots/home.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "应用首页 - 功能导航"}, {"src": "/screenshots/report.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "报告解读 - AI智能分析"}, {"src": "/screenshots/chat.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "健康咨询 - 智能对话"}], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}}