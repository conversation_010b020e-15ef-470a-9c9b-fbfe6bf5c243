#!/bin/bash

# 健康助理前端部署脚本
# 使用方法: ./deploy.sh <version> <environment>
# 示例: ./deploy.sh 1.0.0 dev

set -e

# 检查参数
if [ $# -lt 2 ]; then
    echo "使用方法: $0 <version> <environment>"
    echo "示例: $0 1.0.0 dev"
    exit 1
fi

VERSION=$1
ENVIRONMENT=$2

echo "=== 健康助理前端部署 ==="
echo "版本: $VERSION"
echo "环境: $ENVIRONMENT"

# 步骤1: 构建前端应用 (使用Docker临时容器)
echo "步骤1: 构建前端应用..."
/bin/sh build.sh $VERSION

# 步骤2: 构建Docker镜像
echo "步骤2: 构建Docker镜像..."
docker build --platform linux/amd64 \
  -f Dockerfile \
  --build-arg VERSION=$VERSION \
  -t registry.cn-shanghai.aliyuncs.com/chos/health-assit-agent-frontend-${ENVIRONMENT}:${VERSION} \
  .

# 步骤3: 启动容器
echo "步骤3: 启动容器..."
docker rm -f health-assit-agent-frontend-${ENVIRONMENT} 2>/dev/null || echo "Container not found, skip removal"

docker run --restart always -d \
  -p 8060:8060 \
  -e TZ=Asia/Shanghai \
  --name health-assit-agent-frontend-${ENVIRONMENT} \
  registry.cn-shanghai.aliyuncs.com/chos/health-assit-agent-frontend-${ENVIRONMENT}:${VERSION}

echo "=== 部署完成 ==="
echo "容器名称: health-assit-agent-frontend-${ENVIRONMENT}"
echo "访问地址: http://localhost:8060"
echo "镜像标签: registry.cn-shanghai.aliyuncs.com/chos/health-assit-agent-frontend-${ENVIRONMENT}:${VERSION}"

# 检查容器状态
sleep 3
if docker ps | grep -q health-assit-agent-frontend-${ENVIRONMENT}; then
    echo "✅ 容器启动成功"
    docker ps | grep health-assit-agent-frontend-${ENVIRONMENT}
else
    echo "❌ 容器启动失败"
    docker logs health-assit-agent-frontend-${ENVIRONMENT}
    exit 1
fi
