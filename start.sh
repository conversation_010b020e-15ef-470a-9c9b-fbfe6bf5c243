#!/bin/bash

# 个人健康助理启动脚本
# 支持开发环境和生产环境部署

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js >= 16.0.0"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d 'v' -f 2)
    REQUIRED_VERSION="16.0.0"
    
    if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
        log_error "Node.js 版本过低，当前: $NODE_VERSION，要求: >= $REQUIRED_VERSION"
        exit 1
    fi
    
    log_success "Node.js 版本检查通过: $NODE_VERSION"
    
    # 检查包管理器
    if command -v yarn &> /dev/null; then
        PACKAGE_MANAGER="yarn"
        log_info "使用 Yarn 作为包管理器"
    elif command -v npm &> /dev/null; then
        PACKAGE_MANAGER="npm"
        log_info "使用 npm 作为包管理器"
    else
        log_error "未找到 npm 或 yarn 包管理器"
        exit 1
    fi
}

# 检查环境配置
check_env() {
    log_info "检查环境配置..."
    
    if [ ! -f .env ]; then
        log_warning ".env 文件不存在"
        if [ -f .env.example ]; then
            log_info "正在从 .env.example 创建 .env 文件..."
            cp .env.example .env
            log_warning "请编辑 .env 文件，填入正确的 Dify API 配置"
        else
            log_error "未找到 .env.example 文件"
            exit 1
        fi
    else
        log_success "环境配置文件存在"
    fi
    
    # 检查关键配置项
    if grep -q "your_dify_api_key" .env; then
        log_warning "检测到默认的 API Key，请修改为实际的 Dify API Key"
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    if [ ! -d "node_modules" ]; then
        log_info "首次安装依赖，这可能需要几分钟..."
    fi
    
    if [ "$PACKAGE_MANAGER" = "yarn" ]; then
        yarn install
    else
        npm install
    fi
    
    log_success "依赖安装完成"
}

# 启动开发服务器
start_dev() {
    log_info "启动开发服务器..."
    
    if [ "$PACKAGE_MANAGER" = "yarn" ]; then
        yarn dev
    else
        npm run dev
    fi
}

# 构建生产版本
build_prod() {
    log_info "构建生产版本..."
    
    if [ "$PACKAGE_MANAGER" = "yarn" ]; then
        yarn build
    else
        npm run build
    fi
    
    log_success "构建完成，输出目录: dist/"
}

# 预览生产版本
preview_prod() {
    log_info "预览生产版本..."
    
    if [ ! -d "dist" ]; then
        log_warning "未找到构建文件，正在构建..."
        build_prod
    fi
    
    if [ "$PACKAGE_MANAGER" = "yarn" ]; then
        yarn preview
    else
        npm run preview
    fi
}

# Docker部署
deploy_docker() {
    log_info "Docker 部署..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    # 构建镜像
    log_info "构建 Docker 镜像..."
    docker build -t health-assistant:latest .
    
    # 启动服务
    log_info "启动 Docker 服务..."
    docker-compose up -d
    
    log_success "Docker 部署完成"
    log_info "访问地址: http://localhost:3000"
    
    # 显示日志
    log_info "查看日志: docker-compose logs -f"
}

# 清理缓存
clean_cache() {
    log_info "清理缓存..."
    
    # 清理 node_modules
    if [ -d "node_modules" ]; then
        rm -rf node_modules
        log_success "已清理 node_modules"
    fi
    
    # 清理构建文件
    if [ -d "dist" ]; then
        rm -rf dist
        log_success "已清理 dist"
    fi
    
    # 清理包管理器缓存
    if [ "$PACKAGE_MANAGER" = "yarn" ]; then
        yarn cache clean
    else
        npm cache clean --force
    fi
    
    log_success "缓存清理完成"
}

# 显示帮助信息
show_help() {
    echo "个人健康助理启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  dev       启动开发服务器"
    echo "  build     构建生产版本"
    echo "  preview   预览生产版本"
    echo "  docker    Docker 部署"
    echo "  clean     清理缓存"
    echo "  help      显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 dev      # 启动开发服务器"
    echo "  $0 build    # 构建生产版本"
    echo "  $0 docker   # Docker 部署"
}

# 主函数
main() {
    echo "🩺 个人健康助理 - 启动脚本"
    echo "================================"
    
    # 检查参数
    if [ $# -eq 0 ]; then
        show_help
        exit 1
    fi
    
    case "$1" in
        "dev")
            check_dependencies
            check_env
            install_dependencies
            start_dev
            ;;
        "build")
            check_dependencies
            check_env
            install_dependencies
            build_prod
            ;;
        "preview")
            check_dependencies
            check_env
            preview_prod
            ;;
        "docker")
            deploy_docker
            ;;
        "clean")
            check_dependencies
            clean_cache
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"