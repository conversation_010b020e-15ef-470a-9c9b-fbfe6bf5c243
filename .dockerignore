# Docker构建时忽略的文件和目录

# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 开发文件
.git/
.gitignore
README.md
.prettierrc
.env
.env.*
!.env.example

# 构建输出 (Docker构建时需要dist目录)
# dist/
dist-ssr/

# 编辑器配置
.vscode/
.idea/
*.swp
*.swo
*~

# 系统文件
.DS_Store
Thumbs.db

# 日志
logs/
*.log

# 测试覆盖率
coverage/

# 临时文件
tmp/
temp/

# Docker相关
Dockerfile
.dockerignore
docker-compose.yml
docker-compose.*.yml

# 文档
docs/
*.md
!package.json

# 脚本
start.sh
build.sh
wrap.sh
scripts/

# 备份文件
*.backup
*.bak
*.orig