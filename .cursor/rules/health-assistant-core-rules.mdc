---
description: 
globs: 
alwaysApply: true
---
# 健康助理项目核心开发规则

## 1. React useEffect 死循环预防
**问题**：useEffect 依赖项包含在其内部被修改的状态，导致无限循环调用API
**解决**：将初始化逻辑和状态依赖逻辑分离到不同的useEffect中
```typescript
// ❌ 错误：会导致死循环
useEffect(() => {
  setAppParameters(params) // 修改了依赖项
}, [appParameters])

// ✅ 正确：分离逻辑
useEffect(() => {
  loadAppParameters() // 初始化逻辑，只运行一次
}, [])

useEffect(() => {
  if (appParameters && messages.length === 0) {
    setWelcomeMessage() // 依赖逻辑，明确条件
  }
}, [appParameters, messages.length])
```

## 2. HTTP客户端Content-Type处理
**问题**：全局设置`Content-Type: 'application/json'`导致FormData文件上传失败
**解决**：移除默认Content-Type，让axios根据数据类型自动设置，JSON请求手动指定
```typescript
// ❌ 错误：全局设置影响所有请求
axios.create({
  headers: { 'Content-Type': 'application/json' }
})

// ✅ 正确：按需设置
axios.create({}) // 不设置默认Content-Type

// JSON请求手动设置
post(url, data, {
  headers: { 'Content-Type': 'application/json' }
})
```

## 3. FormData文件上传规范
**核心**：FormData请求绝不设置Content-Type，让浏览器自动设置multipart/form-data边界
```typescript
// ✅ 正确的文件上传
const formData = new FormData()
formData.append('file', file, file.name)
formData.append('user', userId)

// 重要：不设置Content-Type
const response = await httpClient.post(url, formData, {
  headers: { 'Authorization': `Bearer ${apiKey}` }
  // 绝不设置 Content-Type: 'multipart/form-data'
})
```

## 4. 流式响应think标签处理
**关键**：在AI思考过程中不更新正文内容，避免思考内容泄露到用户界面
```typescript
// 关键逻辑：根据think标签状态决定是否更新UI
if (!isInThinkTag) {
  const cleanAnswer = content.replace(/<think>[\s\S]*?<\/think>/g, '').trim()
  callbacks.onMessage?.(cleanAnswer, false)
} else {
  console.log('🧠 正在思考中，跳过正文更新，避免思考内容泄露')
}
```

## 5. API响应格式标准化验证
**原则**：不假设API返回的字段名，需要验证和标准化响应格式
```typescript
// ❌ 错误：直接假设字段存在
if (!response.url) throw error

// ✅ 正确：验证并标准化
const standardizedResponse = {
  id: response.id,
  url: response.preview_url || `${baseUrl}/files/${response.id}`,
  name: response.name,
  // ... 其他字段标准化
}
```

参考文件：[api.ts](mdc:src/utils/api.ts), [HealthConsultation.tsx](mdc:src/pages/HealthConsultation.tsx)
