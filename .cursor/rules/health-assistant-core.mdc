---
description: 
globs: 
alwaysApply: true
---
# 健康助理项目核心开发规则

## 1. 中文优先与简洁原则
- **所有代码注释、接口说明、日志信息必须使用中文**
- **秉承越简单越好的模式，避免过度设计**
- **不要自行创建测试脚本和测试文档，除非明确要求**
- **README文档只在必要时补充重要模块，不要每个功能都添加文档**

## 2. 对话状态管理规范
- **conversation_id是多轮对话的核心，必须正确传递和保存**
- **第一次对话不传conversation_id，后续对话使用API返回的ID**
- **本地存储必须包含conversationId字段，页面刷新后要能恢复对话**
- **参考 [HealthConsultation.tsx](mdc:src/pages/HealthConsultation.tsx) 的状态管理模式**

## 3. 流式响应处理标准
- **使用sendMessageStream方法处理AI对话，支持实时显示**
- **正确处理think标签，分离思考过程和正文内容**
- **思考过程默认折叠，用户可手动展开查看**
- **参考 [api.ts](mdc:src/utils/api.ts) 的流式处理实现**

## 4. 响应式设计要求
- **必须支持移动端(H5)、平板、桌面的完整响应式设计**
- **移动端特定布局：功能卡片使用2列网格(grid-cols-2)**
- **使用Tailwind的响应式断点：xs(375px), sm(640px), md(768px), lg(1024px)**
- **参考 [tailwind.config.js](mdc:tailwind.config.js) 的断点配置**

## 5. 详细日志与调试规范
- **关键操作必须有详细的、可搜索追踪的日志记录**
- **使用emoji前缀区分日志类型：🤖(API调用)、🔗(对话ID)、💾(存储)、🚨(错误)**
- **打印关键对象时注意防止空指针，使用可选链操作符**
- **API配置错误要给出明确提示，如"请配置正确的Dify API Key"**

## 6. 自动滚动机制要求
使用双重保障：scrollIntoView + 直接设置scrollTop = scrollHeight
适当的延迟确保DOM完全渲染（150ms+）
容器padding需精确调整：顶部70-120px，底部130px
为messagesEndRef添加足够高度（如h-4类）便于定位

## 7. 不要假设数据结构
API字段名可能与预期不同，要根据实际返回数据调整解析逻辑
知识库引用在metadata.retriever_resources而不是直接在根级别
先打印原始数据，再写解析代码

## 8. 前端状态同步严格
在onComplete回调中必须处理所有扩展字段(citations、suggestedQuestions)
使用as any类型断言处理扩展响应数据
确保消息对象包含完整的citations字段用于UI渲染

## 9. UI适配要基于实际测试
padding等布局参数要根据实际显示效果调整(如120px vs 70px)
固定定位元素要考虑内容被遮挡的情况
响应式设计要在移动端和桌面端都测试验证

