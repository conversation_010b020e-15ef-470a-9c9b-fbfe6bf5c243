# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 构建输出
dist/
dist-ssr/
*.local

# 编辑器
.vscode/*
!.vscode/extensions.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
logs/
*.log

# 运行时
pids/
*.pid
*.seed
*.pid.lock

# 覆盖率
coverage/
*.lcov

# nyc测试覆盖率
.nyc_output/

# Grunt中间和输出目录
.grunt/

# Bower依赖目录
bower_components/

# node-waf配置
.lock-wscript

# 编译的二进制插件
build/Release

# 依赖目录
jspm_packages/

# TypeScript v1声明文件
typings/

# TypeScript缓存
*.tsbuildinfo

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# Microbundle缓存
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 可选的REPL历史
.node_repl_history

# 输出的构建文件夹
out/

# Nuxt.js构建/生成输出
.nuxt/

# Gatsby文件
.cache/

# Vuepress构建输出
.vuepress/dist/

# Serverless目录
.serverless/

# FuseBox缓存
.fusebox/

# DynamoDB本地文件
.dynamodb/

# TernJS端口文件
.tern-port

# Stores VSCode版本用于测试
.vscode-test/

# yarn v2
.yarn/cache/
.yarn/unplugged/
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# 临时文件
tmp/
temp/

# 系统文件
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
.Spotlight-V100/
.Trashes/

# 备份文件
*.backup
*.bak
*.orig