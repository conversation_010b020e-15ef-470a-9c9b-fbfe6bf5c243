# 📋 个人健康助理 - 项目核心需求文档

## 🎯 项目概述

**个人健康助理** 是一款基于AI驱动的智能健康管理应用，为用户提供全方位的个人健康服务。通过先进的多模态AI技术，实现24小时智能健康管理。

### 🌟 项目愿景
打造中国领先的AI驱动个人健康助理平台，让每个人都能享受专业、便捷、智能的健康管理服务。

### 🔍 两大主线智能体
报告解读：多模态 + OCR 结合 LLM，是刚需、落地性强的切口。

全科医生：面向用户主动问诊与被动自评，是AI问诊的典型场景。

## 🔧 核心功能模块

### 1. 🔬 多模态医疗报告解读
- **智能OCR识别**: 支持各类医疗报告的文字识别
- **AI深度分析**: 结合医学知识库进行智能解读
- **风险评估**: 健康指标异常预警
- **报告类型**: 血常规、尿常规、CT、MRI、X光等

### 2. 🤖 24小时AI智能问诊
- **对话式问诊**: 自然语言交互，模拟真实医生问诊
- **症状智能分析**: 基于症状库的疾病可能性分析
- **智能分诊**: 紧急程度评估和科室推荐
- **专业建议**: 初步诊断建议和就医指导
- **持续对话**: 支持多轮对话和症状跟踪

### 3. 🏥 智能导诊服务
- **医院智能推荐**: 基于位置、需求、评价的综合推荐
- **预约挂号**: 多医院平台集成，统一预约体验
- **就诊导航**: 路线规划和院内导航
- **实时信息**: 医院排队情况、科室信息等
- **医院数据**: 覆盖三甲医院，包含详细医院信息

### 4. 📱 个人健康档案
- **健康数据管理**: 个人基础信息、过敏史、用药史
- **检查报告存储**: 云端安全存储所有医疗报告
- **健康评分**: 综合健康状况评估 (0-100分制)
- **数据同步**: 多设备数据同步
- **隐私保护**: 多层加密，确保数据安全

项目名称：health-assitant-agent
前端技术栈 vite + react + Framer motion + Tailwind CSS  （动画库，下面暂时没有列，但是需要加）
后端：暂时不做开发，先对接dify的接口应用来实现，这些参数都通过配置来实现，然后通过dify的api来实现。
形式：移动端h5(响应式)
部署方式： docker部署


部分依赖库推荐：
    "axios": "^1.5.0",
    "dayjs": "^1.11.9",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.15.0",
    "@types/node": "^22.13.14",
    "@types/react": "^18.3.20",
    "@types/react-dom": "^18.3.5",
    "@vitejs/plugin-react": "^4.0.3",
    "cross-env": "^7.0.3",
    "prettier": "^3.5.3",
    "typescript": "^5.8.2",
    "vite": "^4.5.10"

# 页面设计
暂时不做登录注册，直接进入首页
顶部左边有个标题：个人健康助理

下面有2块区域
第一块区域：
大标题
报告解读智能体
小标题
智能解读检查、检验、体检报告

标题：拍照解读
副标题：拍摄体检报告智能分析

标题：照片解读
副标题：上传PDF/图片智能分析



第二块区域：
大标题
全科医生智能体
小标题
专业健康咨询和问答

标题：AI健康咨询智能体
副标题：专业健康问答

标题：症状自评
副标题：AI症状分析评估

标题：趋势分析
副标题：健康指标变化趋势



# 📋 页面结构功能详细描述

## 🏠 页面入口

* 页面默认进入“首页”，无需登录注册。
* 页面顶部左上角显示标题文字：**个人健康助理**。
* 页面背景颜色为浅灰色（如 `#f8f9fb`），整体风格简洁清晰。

---

## 🧱 页面主内容布局

页面整体内容分为上下两大块区域，对应两个核心功能智能体：

---

### ① 区域一：报告解读智能体（📑）

#### 区域说明：

* 用于对体检报告（拍照或上传）进行OCR和智能分析。
* 包含两个功能卡片，横向排列。

#### 标题：

* **大标题（黑色，18-20px，粗体）：** 报告解读智能体
* **小标题（灰色，14px，常规字体）：** 智能解读检查、检验、体检报告

#### 功能卡片设计（两列）：

##### 📷 功能 1：拍照解读

* **图标：** 蓝色圆形背景，中间为白色相机图标
* **主标题（黑体）：** 拍照解读
* **副标题（灰色文字）：** 拍摄体检报告智能分析
* **功能说明：**

  * 用户点击后可唤起拍照功能（H5 调用摄像头）
  * 拍摄完成后上传图片交由智能体解析报告

##### 🖼️ 功能 2：照片解读

* **图标：** 绿色圆形背景，中间为上传图标（箭头向上）
* **主标题（黑体）：** 照片解读
* **副标题（灰色文字）：** 上传PDF/图片智能分析
* **功能说明：**

  * 用户上传本地已有的体检报告图片或PDF文件
  * 自动交由智能体解读报告内容，生成结构化摘要或建议

---

### ② 区域二：全科医生智能体（🩺）

#### 区域说明：

* 面向用户日常健康咨询与自我评估。
* 包含三个功能卡片，横向或响应式排列为两列+一行。

#### 标题：

* **大标题（黑色，18-20px，粗体）：** 全科医生智能体
* **小标题（灰色，14px，常规字体）：** 专业健康咨询和问答

#### 功能卡片设计（三个）：

##### 💬 功能 1：AI健康咨询智能体

* **图标：** 紫色圆形背景，中间为白色对话气泡图标
* **主标题（黑体）：** AI健康咨询智能体
* **副标题（灰色文字）：** 专业健康问答
* **功能说明：**

  * 模拟全科医生问诊，支持多轮对话
  * 用户可咨询健康相关问题，由LLM智能回答

##### 🧠 功能 2：症状自评

* **图标：** 橙色圆形背景，中间为白色心电图线条（或心跳曲线）
* **主标题（黑体）：** 症状自评
* **副标题（灰色文字）：** AI症状分析评估
* **功能说明：**

  * 用户选择/输入症状信息
  * AI 生成可能疾病列表与就医建议

##### 📈 功能 3：趋势分析

* **图标：** 蓝紫色圆形背景，中间为上升折线图标
* **主标题（黑体）：** 趋势分析
* **副标题（灰色文字）：** 健康指标变化趋势
* **功能说明：**

  * 用户上传历史健康数据（如血压、血糖、BMI 等）
  * 系统展示指标变化曲线 + AI 给出趋势点评与建议

---

## 🧩 交互与样式建议

| 元素    | 建议样式                                              |
| ----- | ------------------------------------------------- |
| 功能卡片  | `rounded-2xl`，`shadow-sm`，`hover:shadow-md`，背景为白色 |
| 卡片内排版 | 上方为图标，中间为主标题，底部为副标题，垂直居中对齐                        |
| 图标样式  | 使用 `lucide-react` 或 `heroicons` 库，背景圆形+中心图标       |
| 布局方式  | 使用 `grid grid-cols-2 md:grid-cols-3` 实现响应式布局      |

---

## 📷 示例图标建议对应（用于 LLM 代码生成时使用）

| 功能     | 图标建议（Lucide 或 Heroicons 名称）        |
| ------ | ---------------------------------- |
| 拍照解读   | `CameraIcon`                       |
| 照片解读   | `UploadCloudIcon` 或 `FileIcon`     |
| AI健康咨询 | `MessageCircleIcon` 或 `BotIcon`    |
| 症状自评   | `ActivityIcon` 或 `HeartPulseIcon`  |
| 趋势分析   | `LineChartIcon` 或 `TrendingUpIcon` |

