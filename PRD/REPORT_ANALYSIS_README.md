# 报告解读功能说明

## 功能概述

报告解读功能支持拍照或上传体检报告图片，AI将自动识别并提供专业的医学解读，包括：

- **综合健康评估**：整体健康评分和风险等级
- **指标详细分析**：各项检验指标的状态和说明
- **健康建议**：饮食、运动、生活方式建议
- **风险预警**：潜在健康风险和预防措施

## 页面路由

- `/report-analysis` - 智能报告解读页面（推荐使用）
- `/report` - 基础报告解读页面

## 技术实现

### API调用方式

使用与健康咨询页面相同的API调用方式：

```typescript
await difyClient.sendMessageWithImages(
  config.dify.reportAgent.appId,
  prompt,
  files,
  callbacks,
  conversationId
)
```

### 数据结构

返回的结构化数据格式：

```typescript
interface AnalysisResult {
  overview: {
    patientName: string
    age: number
    date: string
    overallScore: number // 0-100
    riskLevel: 'low' | 'medium' | 'high'
    summary: string
  }
  indicators: {
    category: string
    status: 'normal' | 'attention' | 'abnormal'
    items: {
      name: string
      value: string
      normalRange: string
      status: 'normal' | 'attention' | 'abnormal'
      description: string
    }[]
  }[]
  healthAdvice: {
    dietary: string[]
    exercise: string[]
    lifestyle: string[]
  }
  riskWarnings: {
    title: string
    description: string
    preventionMeasures: string[]
  }[]
}
```

### 特性

1. **流式响应**：支持实时显示AI思考过程
2. **思考过程**：可折叠的AI分析步骤展示
3. **进度指示**：显示各个数据区域的分析进度
4. **错误处理**：完善的错误处理和用户反馈
5. **Mock数据**：开发环境下提供测试数据

## 配置要求

### 环境变量

```env
VITE_DIFY_BASE_URL=http://dify.taihealth.cn/v1
VITE_DIFY_API_KEY=your-api-key
VITE_DIFY_REPORT_APP_ID=your-report-app-id
```

### 代理配置

开发环境下使用Vite代理解决CORS问题：

```typescript
proxy: {
  '/api/v1': {
    target: 'http://dify.taihealth.cn',
    changeOrigin: true,
    rewrite: (path) => path.replace(/^\/api\/v1/, '/v1')
  }
}
```

## 使用说明

1. **拍照解读**：点击"拍照解读"按钮，使用相机拍摄报告
2. **选择图片**：点击"选择图片"按钮，从相册选择报告图片
3. **等待分析**：AI将自动识别并分析报告内容
4. **查看结果**：查看结构化的解读结果

## 支持的报告类型

- 血常规检查
- 生化全套
- 血脂检查
- 血糖检测
- 尿常规检查
- 肝功能检查
- 肾功能检查
- 体检报告

## 开发测试

开发环境下提供测试按钮，可以使用mock数据测试UI展示效果。

## 注意事项

1. 支持JPG、PNG格式，最大10MB
2. 请确保图片清晰，文字可见
3. 解读结果仅供参考，不能替代专业医生诊断
4. 数据安全处理，隐私保护
