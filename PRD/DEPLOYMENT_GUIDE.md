# 健康助理前端部署指南

## 📋 部署概述

健康助理前端采用Docker容器化部署方案，使用Docker临时容器构建模式，确保跨平台兼容性和部署一致性。

## 🏗️ 部署架构

```
构建阶段:
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   build.sh      │───▶│  Docker临时容器  │───▶│   dist目录      │
│   (主构建脚本)   │    │  node:18-alpine │    │   (构建产物)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘

部署阶段:
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Dockerfile    │───▶│  生产镜像构建    │───▶│   运行容器      │
│   (nginx基础)   │    │  nginx:1.25     │    │   端口:8060     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速部署

### 前置要求

- Docker 20.0+
- 网络连接 (用于拉取镜像和依赖)

### 一键部署

```bash
# 1. 给脚本执行权限
chmod +x build.sh wrap.sh deploy.sh

# 2. 执行部署
./deploy.sh 1.0.0 dev

# 3. 验证部署
curl http://localhost:8060
```

## 📝 详细部署流程

### 步骤1: 构建前端应用

```bash
# 使用Docker临时容器构建
./build.sh 1.0.0
```

**执行过程:**
1. 启动node:18-alpine临时容器
2. 挂载项目目录到容器
3. 执行wrap.sh脚本进行构建
4. 生成dist目录

### 步骤2: 构建Docker镜像

```bash
docker build --platform linux/amd64 \
  -f Dockerfile \
  --build-arg VERSION=1.0.0 \
  -t registry.cn-shanghai.aliyuncs.com/chos/health-assit-agent-frontend-dev:1.0.0 \
  .
```

**镜像特性:**
- 基础镜像: nginx:1.25-alpine
- 工作目录: /opt/app
- 静态文件: /var/www/frontend
- 暴露端口: 8060

### 步骤3: 启动容器

```bash
# 清理旧容器
docker rm -f health-assit-agent-frontend-dev 2>/dev/null || echo "Container not found, skip removal"

# 启动新容器
docker run --restart always -d \
  -p 8060:8060 \
  -e TZ=Asia/Shanghai \
  --name health-assit-agent-frontend-dev \
  registry.cn-shanghai.aliyuncs.com/chos/health-assit-agent-frontend-dev:1.0.0
```

## 🔧 配置说明

### 镜像命名规范

```
registry.cn-shanghai.aliyuncs.com/chos/health-assit-agent-frontend-{environment}:{version}
```

### 容器配置

| 配置项 | 值 | 说明 |
|--------|-----|------|
| 端口映射 | 8060:8060 | 主机端口:容器端口 |
| 容器名称 | health-assit-agent-frontend-{env} | 环境相关命名 |
| 重启策略 | always | 自动重启 |
| 时区 | Asia/Shanghai | 中国时区 |

### 环境变量

构建时注入的环境变量:

```bash
VITE_APP_VERSION=1.0.0  # 应用版本
NODE_ENV=production     # 生产环境
```

## 🛠️ 脚本详解

### build.sh
- **功能**: 主构建脚本
- **参数**: `<version>`
- **原理**: 使用Docker临时容器避免本地环境依赖

### wrap.sh
- **功能**: 容器内构建脚本
- **特性**: 
  - 配置npm镜像源 (registry.npmmirror.com)
  - 设置缓存目录避免权限问题
  - 清理跨平台依赖冲突

### deploy.sh
- **功能**: 一键部署脚本
- **参数**: `<version> <environment>`
- **流程**: 构建应用 → 构建镜像 → 启动容器

## 🔍 故障排除

### 常见问题

1. **Docker权限问题**
   ```bash
   # 解决方案: 添加用户到docker组
   sudo usermod -aG docker $USER
   ```

2. **端口占用**
   ```bash
   # 检查端口占用
   lsof -i :8060
   # 停止占用进程或更换端口
   ```

3. **构建失败**
   ```bash
   # 清理Docker缓存
   docker system prune -a
   # 重新构建
   ./build.sh 1.0.0
   ```

### 日志查看

```bash
# 查看容器日志
docker logs health-assit-agent-frontend-dev

# 实时查看日志
docker logs -f health-assit-agent-frontend-dev
```

## 📊 性能优化

### 镜像优化
- 使用Alpine Linux基础镜像
- 多阶段构建减少镜像体积
- 静态资源Gzip压缩

### 容器优化
- 配置nginx缓存策略
- 启用Gzip压缩
- 优化静态资源服务

## 🔒 安全考虑

- 使用非root用户运行nginx
- 配置安全头信息
- 限制容器资源使用
- 定期更新基础镜像

## 📈 监控建议

- 配置容器健康检查
- 监控容器资源使用
- 设置日志轮转策略
- 配置告警机制
