import react from '@vitejs/plugin-react';
import path from 'path';
import { defineConfig } from 'vite';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    open: true,
    // 代理配置 - 解决CORS跨域问题
    proxy: {
      '/api/v1': {
        target: 'http://dify.taihealth.cn',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/v1/, '/v1'), // 明确保留原路径
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req) => {
            console.log('📤 代理请求:', req.method, req.url, '-> 目标:', proxyReq.getHeader('host') + proxyReq.path)
          });
          proxy.on('proxyRes', (proxyRes, req) => {
            console.log('📥 代理响应:', proxyRes.statusCode, req.url)
          });
        }
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: 'esbuild',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          utils: ['axios', 'dayjs'],
          motion: ['framer-motion'],
          icons: ['lucide-react']
        }
      }
    }
  }
})