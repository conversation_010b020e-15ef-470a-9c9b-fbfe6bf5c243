/**
 * API工具类
 * 封装Dify接口调用和通用HTTP请求
 */

import { config } from '@/config'
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

// 请求接口定义
interface DifyMessage {
  role: 'user' | 'assistant'
  content: string
  timestamp?: string
}

interface DifyRequest {
  inputs: Record<string, any>
  query: string
  response_mode: 'blocking' | 'streaming'
  conversation_id?: string
  user: string
  files?: Array<DifyFile>
}

/**
 * type (string) 支持类型：
document 具体类型包含：'TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'
image 具体类型包含：'JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'
audio 具体类型包含：'MP3', 'M4A', 'WAV', 'WEBM', 'AMR'
video 具体类型包含：'MP4', 'MOV', 'MPEG', 'MPGA'
custom 具体类型包含：其他文件类型
transfer_method (string) 传递方式:
remote_url: 图片地址。
local_file: 上传文件。
url 图片地址。（仅当传递方式为 remote_url 时）。
upload_file_id 上传文件 ID。（仅当传递方式为 local_file 时）。
 */
interface DifyFile {
  type: string
  transfer_method: string
  url?: string
  upload_file_id?: string
}

interface DifyResponse {
  event: string
  message_id: string
  conversation_id: string
  answer: string
  created_at: number
  metadata?: {
    usage?: {
      prompt_tokens: number
      completion_tokens: number
      total_tokens: number
    }
    retriever_resources?: Array<{
      document_id: string
      document_name: string
      content: string
      segment_id: string
      score: number
      position: number
      hit_count: number
      word_count: number
      segment_position: number
      index_node_id: string
      index_node_hash: string
    }>
  }
}

// 流式响应事件类型
interface DifyStreamEvent {
  event: 'message' | 'agent_message' | 'agent_thought' | 'message_end' | 'message_replace' | 'error' | 'ping' | 'workflow_started' | 'workflow_finished' | 'node_started' | 'node_finished'
  conversation_id?: string
  message_id?: string
  answer?: string
  task_id?: string
  id?: string
  data?: any
  created_at?: number
  metadata?: {
    usage?: {
      prompt_tokens: number
      completion_tokens: number
      total_tokens: number
    }
  }
}

// 思考过程接口
interface AgentThought {
  id: string
  message_id: string
  position: number
  thought: string
  tool: string | null
  tool_input: string | null
  created_at: number
}

// 流式回调接口
interface StreamCallbacks {
  onMessage?: (content: string, isComplete: boolean, isIncremental?: boolean) => void
  onThought?: (thought: AgentThought) => void
  onError?: (error: Error) => void
  onComplete?: (response: DifyResponse) => void
}

// RAG知识库引用接口
interface RAGCitation {
  title: string
  content: string
  source: string
  score?: number
}

// 消息反馈接口
interface MessageFeedback {
  rating: 'like' | 'dislike'
  content?: string
}

// 应用开场白接口
interface AppParameters {
  opening_statement?: string
  suggested_questions?: string[]
}

// 扩展DifyResponse接口
interface ExtendedDifyResponse extends DifyResponse {
  citations?: RAGCitation[]
  suggested_questions?: string[]
}

// API错误类型
class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message)
    this.name = 'ApiError'
  }
}


// HTTP客户端类
class HttpClient {
  private instance: AxiosInstance

  constructor() {
    this.instance = axios.create({
      timeout: config.api.timeout,
      // 🔥 修复：移除默认的Content-Type，让axios根据数据类型自动设置
      // 对于FormData，axios会自动设置为 multipart/form-data
      // 对于JSON，我们会在具体请求中指定
    })

    // 请求拦截器
    this.instance.interceptors.request.use(
      config => {
        console.log(`📤 API请求: ${config.method?.toUpperCase()} ${config.url}`)
        return config
      },
      error => {
        console.error('📤 请求拦截器错误:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(`📥 API响应: ${response.status} ${response.config.url}`)
        return response
      },
      error => {
        console.error('📥 响应错误:', error?.response?.data || error.message)

        const status = error?.response?.status || 0
        const message = error?.response?.data?.message || error.message

        throw new ApiError(message, status)
      }
    )
  }

  // 通用请求方法
  async request<T = any>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.instance.request<T>(config)
      return response.data
    } catch (error) {
      console.error('🚨 请求失败:', error)
      throw error
    }
  }

  // GET请求
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'GET', url })
  }

  // POST请求
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'POST', url, data })
  }

  // PUT请求
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'PUT', url, data })
  }

  // DELETE请求
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'DELETE', url })
  }
}

// Dify API客户端类
class DifyApiClient {
  private httpClient: HttpClient
  private baseUrl: string
  private apiKey: string
  private readonly USER_SESSION_KEY = 'user_sessions' // localStorage键名

  constructor() {
    this.httpClient = new HttpClient()
    this.baseUrl = config.dify.baseUrl
    this.apiKey = config.dify.apiKey

    if (!this.apiKey) {
      console.warn('⚠️ Dify API Key未配置')
    }

    // 初始化时显示已存储的用户会话映射
    const existingSessions = this.getUserSessionMap()
    if (Object.keys(existingSessions).length > 0) {
      console.log('🔄 恢复用户会话映射:', existingSessions)
    } else {
      console.log('🔄 没有已存储的用户会话映射')
    }
  }

  // 获取认证头
  private getAuthHeaders() {
    return {
      Authorization: `Bearer ${this.apiKey}`,
    }
  }

  // 从localStorage获取用户会话映射
  private getUserSessionMap(): Record<string, string> {
    try {
      const stored = localStorage.getItem(this.USER_SESSION_KEY)
      return stored ? JSON.parse(stored) : {}
    } catch (error) {
      console.error('❌ 读取用户会话映射失败:', error)
      return {}
    }
  }

  // 保存用户会话映射到localStorage
  private saveUserSessionMap(sessionMap: Record<string, string>) {
    try {
      localStorage.setItem(this.USER_SESSION_KEY, JSON.stringify(sessionMap))
      console.log('💾 用户会话映射已保存到localStorage')
    } catch (error) {
      console.error('❌ 保存用户会话映射失败:', error)
    }
  }

  // 获取或生成固定的用户ID
  getUserId(conversationId?: string): string {
    const sessionMap = this.getUserSessionMap()
    
    // 如果有conversationId，尝试从localStorage中获取对应的userId
    if (conversationId && sessionMap[conversationId]) {
      const userId = sessionMap[conversationId]
      console.log(`🔗 使用已存在的用户ID: ${userId} (对话ID: ${conversationId})`)
      return userId
    }

    // 生成新的用户ID
    const newUserId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    // 如果有conversationId，建立映射关系并保存
    if (conversationId) {
      sessionMap[conversationId] = newUserId
      this.saveUserSessionMap(sessionMap)
      console.log(`📝 建立用户映射: ${conversationId} -> ${newUserId}`)
    }
    
    console.log(`🆕 生成新用户ID: ${newUserId}`)
    return newUserId
  }

  // 获取完整的请求URL（处理开发环境代理）
  private getFullUrl(path: string): string {
    // 在开发环境下，baseUrl 已经是 '/api/v1'，直接拼接即可
    // 在生产环境下，baseUrl 是完整的 URL
    return `${this.baseUrl}${path}`
  }

  // 发送消息到智能体 - 流式响应
  async sendMessageStream(
    appId: string,
    message: string,
    callbacks: StreamCallbacks,
    conversationId?: string,
    files?: DifyFile[]
  ): Promise<void> {
    try {
      console.log(`🤖 发送流式消息到智能体 ${appId}:`, message)
      console.log(`🔗 使用对话ID:`, conversationId || '(新对话)')
      console.log('发送流式消息中files:', files)

      // 检查回调函数是否存在
      console.log('🔍 检查回调函数:', {
        onMessage: typeof callbacks.onMessage,
        onThought: typeof callbacks.onThought,
        onComplete: typeof callbacks.onComplete,
        onError: typeof callbacks.onError,
      })

      // 获取固定的用户ID
      const userId = this.getUserId(conversationId)

      const requestData: DifyRequest = {
        inputs: {},
        query: message,
        response_mode: 'streaming',
        user: userId,
        files: files,
      }

      // 只有在有有效的conversationId时才添加到请求中
      if (conversationId && conversationId.trim()) {
        console.log(`📝 添加对话ID到请求: ${conversationId}`)
        requestData.conversation_id = conversationId
      } else {
        console.log(`📝 开始新对话 (没有提供有效的conversationId)`)
      }

      console.log('📤 发送流式请求数据:', JSON.stringify(requestData, null, 2))

      // 构建正确的URL
      const url = this.getFullUrl('/chat-messages')
      console.log('🌐 流式请求URL:', url)
      console.log('🔑 API密钥状态:', this.apiKey ? '已配置' : '未配置')
      console.log('🆔 应用ID:', appId)

      // 使用fetch进行流式请求
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
          'X-App-ID': appId,
        },
        body: JSON.stringify(requestData),
      })

      console.log('📡 流式响应状态:', response.status, response.statusText)
      console.log('📡 响应头:', Object.fromEntries(response.headers.entries()))

      if (!response.ok) {
        throw new ApiError(`HTTP ${response.status}: ${response.statusText}`, response.status)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new ApiError('无法获取响应流', 500)
      }

      const decoder = new TextDecoder()
      let buffer = ''
      let finalResponse: DifyResponse | null = null
      let accumulatedAnswer = '' // 累积答案内容
      let currentThinkContent = '' // 当前思考内容
      let isInThinkTag = false // 是否在think标签内
      let thinkId = '' // 思考ID，保持唯一
      let previousCleanAnswer = '' // 上一次清理后的答案内容，用于计算增量

      console.log('📥 开始处理流式响应...')

      try {
        while (true) {
          const { done, value } = await reader.read()
          
          if (done) {
            console.log('✅ 流式响应完成')
            break
          }

          // 将新数据添加到缓冲区
          buffer += decoder.decode(value, { stream: true })
          
          // 处理缓冲区中的完整行
          const lines = buffer.split('\n')
          buffer = lines.pop() || '' // 保留最后一个不完整的行

          for (const line of lines) {
            const trimmedLine = line.trim()
            if (!trimmedLine) {
              continue
            }

            if (!trimmedLine.startsWith('data: ')) {
              continue
            }

            const data = trimmedLine.slice(6) // 移除 'data: ' 前缀
            console.log('📦 原始流式数据:', data.substring(0, 200) + (data.length > 200 ? '...' : ''))

            if (data === '[DONE]') {
              console.log('🏁 收到流式结束标记')
              continue
            }

            try {
              const event: any = JSON.parse(data)
              console.log('🔍 收到流式事件:', {
                event: event.event,
                hasAnswer: !!event.answer,
                answerLength: event.answer?.length || 0,
                answerPreview: event.answer?.substring(0, 50) || '',
                fullEvent: event
              })

              switch (event.event) {
                case 'message':
                case 'agent_message':
                  // 处理消息内容，检查是否包含think标签
                  const messageContent = event.answer || event.data || event.content || ''
                  if (messageContent) {
                    console.log('📝 收到增量内容:', JSON.stringify(messageContent))
                    const content = messageContent
                    
                    // 累积内容用于think标签处理
                    accumulatedAnswer += content
                    console.log('📚 累积内容长度:', accumulatedAnswer.length)
                    
                    // 检查think标签的开始
                    if (content.includes('<think>') && !isInThinkTag) {
                      isInThinkTag = true
                      currentThinkContent = ''
                      thinkId = `think_${Date.now()}_${event.message_id || 'unknown'}`
                      console.log('🧠 开始思考过程，ID:', thinkId)
                    }
                    
                    if (isInThinkTag) {
                      // 提取think标签内的内容
                      const thinkMatch = accumulatedAnswer.match(/<think>([\s\S]*?)(?:<\/think>|$)/);
                      if (thinkMatch) {
                        const newThinkContent = thinkMatch[1];
                        if (newThinkContent !== currentThinkContent) {
                          currentThinkContent = newThinkContent;
                          
                          // 创建或更新思考对象
                          const thought: AgentThought = {
                            id: thinkId, // 使用固定的ID
                            message_id: event.message_id || '',
                            position: 1, // 固定为1，因为只有一个思考过程
                            thought: currentThinkContent,
                            tool: 'AI思考',
                            tool_input: null,
                            created_at: Date.now(),
                          }
                          
                          console.log('🧠 AI思考内容更新，长度:', currentThinkContent.length)
                          callbacks.onThought?.(thought)
                        }
                      }
                    }
                    
                    // 检查think标签的结束
                    if (content.includes('</think>') && isInThinkTag) {
                      isInThinkTag = false
                      console.log('🧠 思考过程结束')
                      
                      // 发送思考结束的标记，在thought内容后添加特殊标记
                      const finalThought: AgentThought = {
                        id: thinkId,
                        message_id: event.message_id || '',
                        position: 1,
                        thought: currentThinkContent + '###THINKING_COMPLETED###', // 添加特殊标记
                        tool: 'AI思考',
                        tool_input: null,
                        created_at: Date.now(),
                      }
                      
                      // 通过特殊的回调标记思考结束
                      callbacks.onThought?.(finalThought)
                    }
                    
                    // 🔥 修复关键bug：只有在不在思考状态时才更新正文内容
                    if (!isInThinkTag) {
                      // 移除think标签，只显示正文内容
                      const cleanAnswer = accumulatedAnswer.replace(/<think>[\s\S]*?<\/think>/g, '').trim()
                      console.log('🧹 清理think标签后内容长度:', cleanAnswer.length, '内容预览:', cleanAnswer.substring(0, 100))
                      
                      // 只有当清理后的内容有变化时才更新UI
                      if (cleanAnswer !== previousCleanAnswer) {
                        console.log('💬 正文内容更新，长度:', cleanAnswer.length, '增量:', cleanAnswer.length - previousCleanAnswer.length)
                        callbacks.onMessage?.(cleanAnswer, false)
                        previousCleanAnswer = cleanAnswer
                      } else {
                        console.log('⏭️ 内容无变化，跳过更新')
                      }
                    } else {
                      // 🔥 在思考过程中，不更新正文内容，避免思考内容泄露
                      console.log('🧠 正在思考中，跳过正文更新，避免思考内容泄露')
                    }
                  }
                  break

                case 'message_end':
                  // 最终处理
                  const finalCleanAnswer = accumulatedAnswer.replace(/<think>[\s\S]*?<\/think>/g, '').trim()
                  console.log('🏁 消息结束，最终清理内容长度:', finalCleanAnswer.length)
                  callbacks.onMessage?.(finalCleanAnswer, true)
                  console.log('🏁 消息结束，消息事件元数据:', message, event.metadata)
                  // 🔥 解析知识库引用信息
                  let citations: RAGCitation[] = []
                  if (event.metadata?.retriever_resources && event.metadata.retriever_resources.length > 0) {
                    console.log('📚 检测到知识库引用资源:', event.metadata.retriever_resources.length, '条')
                    citations = event.metadata.retriever_resources.map((resource: any) => ({
                      title: resource.document_name || `知识库文档 ${resource.document_id}`,
                      content: resource.content || '',
                      source: resource.document_name || resource.document_id || '',
                      score: resource.score || 0
                    }))
                    console.log('📚 转换后的引用信息:', citations)
                  }
                  
                  // 构建最终响应
                  finalResponse = {
                    event: event.event,
                    message_id: event.message_id || '',
                    conversation_id: event.conversation_id || '',
                    answer: finalCleanAnswer,
                    created_at: event.created_at || Date.now(),
                    metadata: event.metadata,
                  } as ExtendedDifyResponse
                  
                  // 🔥 添加知识库引用到响应中
                  if (citations.length > 0) {
                    (finalResponse as ExtendedDifyResponse).citations = citations
                  }

                  // 保存新对话的用户映射
                  if (finalResponse.conversation_id && !conversationId) {
                    const sessionMap = this.getUserSessionMap()
                    sessionMap[finalResponse.conversation_id] = userId
                    this.saveUserSessionMap(sessionMap)
                    console.log(`🔗 新对话建立用户映射: ${finalResponse.conversation_id} -> ${userId}`)
                  }
                  
                  callbacks.onComplete?.(finalResponse)
                  break

                case 'error':
                  const error = new ApiError(event.data?.message || '流式响应错误', 500)
                  console.error('🚨 流式响应错误:', error)
                  callbacks.onError?.(error)
                  return

                case 'message_replace':
                  // 处理消息替换事件
                  const replaceContent = event.answer || event.data || event.content || ''
                  if (replaceContent) {
                    console.log('🔄 消息替换事件，新内容长度:', replaceContent.length)
                    accumulatedAnswer = replaceContent // 替换而不是累加
                    
                    // 🔥 检查替换内容是否包含think标签，如果包含则进入思考状态
                    if (replaceContent.includes('<think>') && !replaceContent.includes('</think>')) {
                      isInThinkTag = true
                      console.log('🧠 消息替换时检测到思考开始')
                    } else if (replaceContent.includes('</think>')) {
                      isInThinkTag = false
                      console.log('🧠 消息替换时检测到思考结束')
                    }
                    
                    // 🔥 只有在不在思考状态时才更新正文内容
                    if (!isInThinkTag) {
                      // 移除think标签，只显示正文内容
                      const cleanAnswer = accumulatedAnswer.replace(/<think>[\s\S]*?<\/think>/g, '').trim()
                      console.log('💬 替换后内容更新，长度:', cleanAnswer.length)
                      callbacks.onMessage?.(cleanAnswer, false)
                      previousCleanAnswer = cleanAnswer
                    } else {
                      console.log('🧠 替换时正在思考中，跳过正文更新')
                    }
                  }
                  break

                default:
                  console.log('ℹ️ 未处理的事件类型:', event.event, '完整事件:', event)
              }
            } catch (parseError) {
              console.warn('⚠️ 解析流式数据失败:', parseError, '原始数据:', data.substring(0, 100))
            }
          }
        }
      } finally {
        reader.releaseLock()
      }

    } catch (error) {
      console.error('🚨 流式请求失败:', error)
      const apiError = error instanceof ApiError ? error : new ApiError('流式请求失败', 500)
      console.log('🔥 调用 onError 回调...')
      callbacks.onError?.(apiError)
    }
  }

  // 发送消息到智能体 - 保持原有的阻塞式方法作为备用
  async sendMessage(
    appId: string,
    message: string,
    conversationId?: string,
    files?: File[]
  ): Promise<DifyResponse> {
    try {
      console.log(`🤖 发送消息到智能体 ${appId}:`, message)
      console.log(`🔗 使用对话ID:`, conversationId || '(新对话)')

      // 获取固定的用户ID
      const userId = this.getUserId(conversationId)

      const requestData: DifyRequest = {
        inputs: {},
        query: message,
        response_mode: 'streaming', // 阻塞式请求
        user: userId, // 使用固定的用户ID
      }

      // 只有在有有效的conversationId时才添加到请求中
      if (conversationId && conversationId.trim()) {
        console.log(`📝 添加对话ID到请求: ${conversationId}`)
        requestData.conversation_id = conversationId
      } else {
        console.log(`📝 开始新对话 (没有提供有效的conversationId)`)
      }

      // 处理文件上传
      if (files && files.length > 0) {
        // 这里需要先上传文件获取URL，然后添加到请求中
        console.log('📎 检测到文件附件，需要先上传文件')
        // TODO: 实现文件上传逻辑
      }

      console.log('📤 发送请求数据:', JSON.stringify(requestData, null, 2))

      const response = await this.httpClient.post<DifyResponse>(
        `${this.baseUrl}/chat-messages`,
        requestData,
        {
          headers: {
            ...this.getAuthHeaders(),
            'Content-Type': 'application/json', // 🔥 明确设置JSON请求的Content-Type
            'X-App-ID': appId,
          },
        }
      )

      console.log('🎯 智能体响应:', response.answer)
      console.log('🆔 响应对话ID:', response.conversation_id)
      
      // 如果是新对话，建立用户ID映射关系
      if (response.conversation_id && !conversationId) {
        const sessionMap = this.getUserSessionMap()
        sessionMap[response.conversation_id] = userId
        this.saveUserSessionMap(sessionMap)
        console.log(`🔗 新对话建立用户映射: ${response.conversation_id} -> ${userId}`)
      }
      
      console.log('📥 完整响应数据:', JSON.stringify(response, null, 2))
      return response
    } catch (error) {
      console.error('🚨 智能体通信失败:', error)
      console.error('🚨 完整错误信息:', JSON.stringify(error, null, 2))
      
      throw new ApiError(
        error instanceof ApiError ? error.message : '智能体服务暂时不可用',
        error instanceof ApiError ? error.status : 500
      )
    }
  }

  // 获取对话历史
  async getConversation(conversationId: string): Promise<DifyMessage[]> {
    try {
      console.log(`📋 获取对话历史: ${conversationId}`)


      const response = await this.httpClient.get(
        `${this.baseUrl}/conversations/${conversationId}/messages`,
        {
          headers: this.getAuthHeaders(),
        }
      )

      return response.data || []
    } catch (error) {
      console.error('🚨 获取对话历史失败:', error)
      return []
    }
  }

  // 上传文件
  async uploadFile(file: File): Promise<string> {
    try {
      console.log(`📁 上传文件: ${file.name} (${file.size} bytes)`)

      const formData = new FormData()
      formData.append('file', file)

      const response = await this.httpClient.post(`${this.baseUrl}/files/upload`, formData, {
        headers: {
          ...this.getAuthHeaders(),
          // 🔥 移除Content-Type，让浏览器自动设置multipart/form-data
        },
      })

      console.log('✅ 文件上传成功:', response.id)
      return response.id
    } catch (error) {
      console.error('🚨 文件上传失败:', error)
      throw new ApiError('文件上传失败，请检查文件格式和大小', 500)
    }
  }

  // 清理用户会话映射（用于重置对话或清理内存）
  clearUserSession(conversationId?: string) {
    if (conversationId) {
      // 清理特定对话的映射
      const sessionMap = this.getUserSessionMap()
      delete sessionMap[conversationId]
      this.saveUserSessionMap(sessionMap)
      console.log(`🗑️ 清理用户会话映射: ${conversationId}`)
    } else {
      // 清理所有映射
      this.saveUserSessionMap({})
      console.log('🗑️ 清理所有用户会话映射')
    }
  }

  // 获取当前用户会话信息（调试用）
  getUserSessionInfo() {
    console.log('📊 当前用户会话映射:', this.getUserSessionMap())
    return this.getUserSessionMap()
  }

  // 发送消息反馈（点赞/踩）
  async sendMessageFeedback(
    messageId: string,
    feedback: MessageFeedback,
    userId: string
  ): Promise<void> {
    try {
      console.log(`👍👎 发送消息反馈:`, { messageId, feedback, userId })
      
      // 🔥 检查messageId格式，确保是Dify返回的真实message_id而不是前端生成的ID
      if (messageId.startsWith('assistant_') || messageId.startsWith('user_')) {
        console.warn('⚠️ 检测到前端生成的消息ID，反馈功能需要使用Dify API返回的真实message_id')
        throw new ApiError('无法为此消息发送反馈，请重新发送消息后再试', 400)
      }
      
      const url = this.getFullUrl(`/messages/${messageId}/feedbacks`)
      console.log('📡 反馈请求URL:', url)
      
      // 🔥 修复：使用传入的userId而不是生成新的
      const requestData = {
        rating: feedback.rating,
        content: feedback.content || '',
        user: userId // 使用传入的userId
      }
      
      console.log('📤 反馈请求数据:', requestData)
      
      await this.httpClient.post(url, requestData, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
        },
      })
      
      console.log('✅ 消息反馈发送成功')
    } catch (error) {
      console.error('❌ 发送消息反馈失败:', error)
      throw error
    }
  }

  // 获取应用参数（开场白和建议问题）
  async getAppParameters(appId: string): Promise<AppParameters> {
    try {
      console.log(`📋 获取应用参数:`, appId)
      
      const url = this.getFullUrl('/parameters')
      
      const response = await this.httpClient.get(url, {
        headers: {
          ...this.getAuthHeaders(),
          'X-App-ID': appId,
        },
      })
      
      console.log('✅ 应用参数获取成功:', response)
      return response
    } catch (error) {
      console.error('❌ 获取应用参数失败:', error)
      // 返回默认值而不是抛出错误
      return {
        opening_statement: '您好！我是您的AI健康顾问，很高兴为您服务！🩺',
        suggested_questions: [
          '我最近感觉身体疲劳，可能是什么原因？',
          '如何保持良好的作息习惯？',
          '有什么健康的饮食建议吗？'
        ]
      }
    }
  }

  // 上传图片文件（支持多模态）
  async uploadImageFile(file: File): Promise<{
    id: string
    url: string
    name: string
    size: number
    extension: string
    mime_type?: string
    created_at?: number
  }> {
    try {
      console.log(`📷 上传图片文件:`, {
        name: file.name,
        size: apiUtils.formatFileSize(file.size),
        type: file.type
      })

      // 验证文件类型（支持更多图片格式）
      const allowedImageTypes = [
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/gif',
        'image/webp',
        'image/svg+xml'
      ]
      
      if (!allowedImageTypes.includes(file.type)) {
        throw new ApiError(`不支持的图片格式: ${file.type}`, 400)
      }

      // 验证文件大小
      if (!apiUtils.validateFileSize(file)) {
        throw new ApiError(`文件大小超过限制 (${config.upload.maxSize}MB)`, 400)
      }

      // 🔥 修复上传逻辑：正确构造FormData
      const formData = new FormData()
      formData.append('file', file, file.name) // 添加文件名参数
      // 🔥 修复：图片上传时也应该使用与对话关联的userId，但由于上传时可能还没有conversationId，
      // 这里先生成一个临时的userId，后续在发送消息时会建立正确的映射关系
      const userId = this.getUserId()
      formData.append('user', userId)

      console.log('🔍 FormData调试信息:', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        userId: userId
      })

      const url = this.getFullUrl('/files/upload')
      console.log('📡 上传请求URL:', url)
      
      // 🔥 修复请求配置：确保正确设置headers
      const uploadConfig = {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          // 🚨 重要：不要设置 Content-Type，让浏览器自动设置为 multipart/form-data
          // 'Content-Type': 'multipart/form-data' // ❌ 这样设置会导致边界丢失
        },
        timeout: 30000, // 30秒超时
        maxContentLength: Infinity,
        maxBodyLength: Infinity
      }

      console.log('📤 发送上传请求...')
      const response = await this.httpClient.post(url, formData, uploadConfig)

      console.log('✅ 图片上传成功:', response)
      
      // 🔥 修复响应格式验证：Dify API实际返回的字段
      if (!response.id) {
        console.error('⚠️ 上传响应格式异常:', response)
        throw new ApiError('上传响应格式不正确', 500)
      }

      // 🔥 构建标准化的响应格式，因为Dify可能没有直接返回url字段
      const standardizedResponse = {
        id: response.id,
        url: response.preview_url || `${this.baseUrl}/files/${response.id}`, // 如果没有preview_url，构建一个标准URL
        name: response.name,
        size: response.size,
        extension: response.extension,
        mime_type: response.mime_type,
        created_at: response.created_at
      }

      console.log('🔄 标准化响应格式:', standardizedResponse)
      return standardizedResponse
    } catch (error) {
      const apiError = error as ApiError
      console.error('❌ 图片上传失败:', {
        error: apiError.message || '未知错误',
        status: apiError.status || 'unknown',
        fileName: file.name,
        fileSize: file.size
      })
      
      // 提供更友好的错误消息
      if (apiError.status === 400) {
        throw new ApiError('文件格式或大小不符合要求', 400)
      } else if (apiError.status === 401) {
        throw new ApiError('API密钥无效，请检查配置', 401)
      } else if (apiError.status === 413) {
        throw new ApiError('文件太大，请选择较小的文件', 413)
      } else {
        throw new ApiError(`上传失败: ${apiError.message || '未知错误'}`, apiError.status || 500)
      }
    }
  }

  // 发送包含图片的消息
  async sendMessageWithImages(
    appId: string,
    message: string,
    imageFiles: File[],
    callbacks: StreamCallbacks,
    conversationId?: string
  ): Promise<void> {
    try {
      console.log(`📸 发送包含图片的消息:`, {
        message: message.substring(0, 100),
        imageCount: imageFiles.length,
        conversationId
      })

      // 首先上传所有图片
      const uploadPromises = imageFiles.map(file => this.uploadImageFile(file))
      const uploadedImages = await Promise.all(uploadPromises)
      
      console.log('📋 所有图片上传完成:', uploadedImages)

      // 构建带图片的富文本提示词
      const imageDescriptions = uploadedImages.map((img, index) => 
        `图片${index + 1}: ${img.name}`
      ).join('\n')

      const enhancedMessage = `用户上传了${imageFiles.length}张图片并咨询：${message}

图片信息：
${imageDescriptions}

请您作为专业的健康顾问，仔细分析这些图片内容，结合用户的问题，给出专业、详细的健康建议。请注意：
1. 如果图片与健康相关（如体检报告、皮肤症状、药品说明等），请进行专业分析
2. 如果图片与健康无关，请友善提醒用户这可能不是健康咨询相关的内容
3. 始终强调您的建议仅供参考，严重情况需就医
4. 分析要详细具体，但避免给出明确的诊断结论

请基于图片内容和用户问题给出您的专业建议。`

      // 🔥 修复文件数据构建：根据Dify API规范，上传后的文件应该使用local_file方式
      const files = uploadedImages.map(img => ({
        type: 'image',
        transfer_method: 'local_file', // 使用已上传文件的方式
        upload_file_id: img.id // 使用上传后返回的文件ID
      }))

      console.log('📋 构建的文件数据:', files)

      // 构建请求数据
      const requestData: DifyRequest = {
        inputs: {},
        query: enhancedMessage,
        response_mode: 'streaming',
        user: this.getUserId(conversationId),
        files: files
      }

      if (conversationId && conversationId.trim()) {
        requestData.conversation_id = conversationId
      }

      console.log('📤 发送带图片的流式请求:', {
        queryLength: requestData.query.length,
        filesCount: requestData.files?.length,
        conversationId: requestData.conversation_id
      })

      // 发送流式请求
      await this.sendMessageStream(appId, enhancedMessage, callbacks, conversationId, files)
      
    } catch (error) {
      console.error('❌ 发送包含图片的消息失败:', error)
      throw error
    }
  }
}

// 创建API客户端实例
export const httpClient = new HttpClient()
export const difyClient = new DifyApiClient()

// 导出类型和错误类
export {
  ApiError, type AgentThought, type AppParameters, type DifyMessage,
  type DifyRequest,
  type DifyResponse,
  type DifyStreamEvent, type ExtendedDifyResponse, type MessageFeedback, type RAGCitation, type StreamCallbacks
}

// API工具函数
export const apiUtils = {
  // 重试机制
  async retry<T>(fn: () => Promise<T>, maxRetries = config.api.retryCount): Promise<T> {
    let lastError: Error

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error as Error
        if (i < maxRetries) {
          const delay = Math.pow(2, i) * 1000 // 指数退避
          console.log(`🔄 第${i + 1}次重试，${delay}ms后重试...`)
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }

    throw lastError!
  },

  // 格式化文件大小
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // 验证文件类型
  validateFileType(file: File): boolean {
    return config.upload.allowedTypes.includes(file.type)
  },

  // 验证文件大小
  validateFileSize(file: File): boolean {
    const maxSizeBytes = config.upload.maxSize * 1024 * 1024
    return file.size <= maxSizeBytes
  },
}
