/**
 * 工具函数集合
 * 提供常用的工具函数和辅助方法
 */

import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'

// 扩展dayjs插件
dayjs.extend(relativeTime)

// 设置dayjs中文本地化
dayjs.locale('zh-cn')

/**
 * 日期时间工具
 */
export const dateUtils = {
  // 格式化日期
  format(date: Date | string | number, format = 'YYYY-MM-DD HH:mm:ss'): string {
    return dayjs(date).format(format)
  },

  // 相对时间
  fromNow(date: Date | string | number): string {
    return dayjs(date).fromNow()
  },

  // 获取当前时间戳
  now(): number {
    return Date.now()
  },

  // 判断是否是今天
  isToday(date: Date | string | number): boolean {
    return dayjs(date).isSame(dayjs(), 'day')
  },

  // 判断是否是本周
  isThisWeek(date: Date | string | number): boolean {
    return dayjs(date).isSame(dayjs(), 'week')
  },
}

/**
 * 字符串工具
 */
export const stringUtils = {
  // 截断文本
  truncate(text: string, length: number, suffix = '...'): string {
    if (text.length <= length) return text
    return text.substring(0, length) + suffix
  },

  // 移除HTML标签
  stripHtml(html: string): string {
    const tmp = document.createElement('DIV')
    tmp.innerHTML = html
    return tmp.textContent || tmp.innerText || ''
  },

  // 高亮关键词
  highlight(text: string, keyword: string): string {
    if (!keyword) return text
    const regex = new RegExp(`(${keyword})`, 'gi')
    return text.replace(regex, '<mark>$1</mark>')
  },

  // 生成随机字符串
  random(length = 8): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  },

  // 验证邮箱
  isEmail(email: string): boolean {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return regex.test(email)
  },

  // 验证手机号
  isPhone(phone: string): boolean {
    const regex = /^1[3-9]\d{9}$/
    return regex.test(phone)
  },
}

/**
 * 数组工具
 */
export const arrayUtils = {
  // 数组去重
  unique<T>(array: T[]): T[] {
    return [...new Set(array)]
  },

  // 数组分组
  groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
    return array.reduce(
      (groups, item) => {
        const group = String(item[key])
        groups[group] = groups[group] || []
        groups[group].push(item)
        return groups
      },
      {} as Record<string, T[]>
    )
  },

  // 数组排序
  sortBy<T>(array: T[], key: keyof T, order: 'asc' | 'desc' = 'asc'): T[] {
    return [...array].sort((a, b) => {
      const aVal = a[key]
      const bVal = b[key]

      if (aVal < bVal) return order === 'asc' ? -1 : 1
      if (aVal > bVal) return order === 'asc' ? 1 : -1
      return 0
    })
  },

  // 数组分页
  paginate<T>(array: T[], page: number, pageSize: number): T[] {
    const start = (page - 1) * pageSize
    const end = start + pageSize
    return array.slice(start, end)
  },
}

/**
 * 对象工具
 */
export const objectUtils = {
  // 深度克隆
  deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime()) as any
    if (obj instanceof Array) return obj.map(item => objectUtils.deepClone(item)) as any

    const cloned = {} as T
    for (const key in obj) {
      cloned[key] = objectUtils.deepClone(obj[key])
    }
    return cloned
  },

  // 对象合并
  merge<T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T {
    return Object.assign({}, target, ...sources)
  },

  // 获取嵌套属性
  get(obj: any, path: string, defaultValue?: any): any {
    const keys = path.split('.')
    let result = obj

    for (const key of keys) {
      if (result == null || typeof result !== 'object') {
        return defaultValue
      }
      result = result[key]
    }

    return result !== undefined ? result : defaultValue
  },

  // 设置嵌套属性
  set(obj: any, path: string, value: any): void {
    const keys = path.split('.')
    const lastKey = keys.pop()!

    let current = obj
    for (const key of keys) {
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {}
      }
      current = current[key]
    }

    current[lastKey] = value
  },
}

/**
 * 存储工具
 */
export const storageUtils = {
  // localStorage封装
  local: {
    set(key: string, value: any): void {
      try {
        localStorage.setItem(key, JSON.stringify(value))
      } catch (error) {
        console.error('localStorage设置失败:', error)
      }
    },

    get<T>(key: string, defaultValue?: T): T | undefined {
      try {
        const item = localStorage.getItem(key)
        return item ? JSON.parse(item) : defaultValue
      } catch (error) {
        console.error('localStorage获取失败:', error)
        return defaultValue
      }
    },

    remove(key: string): void {
      try {
        localStorage.removeItem(key)
      } catch (error) {
        console.error('localStorage删除失败:', error)
      }
    },

    clear(): void {
      try {
        localStorage.clear()
      } catch (error) {
        console.error('localStorage清空失败:', error)
      }
    },
  },

  // sessionStorage封装
  session: {
    set(key: string, value: any): void {
      try {
        sessionStorage.setItem(key, JSON.stringify(value))
      } catch (error) {
        console.error('sessionStorage设置失败:', error)
      }
    },

    get<T>(key: string, defaultValue?: T): T | undefined {
      try {
        const item = sessionStorage.getItem(key)
        return item ? JSON.parse(item) : defaultValue
      } catch (error) {
        console.error('sessionStorage获取失败:', error)
        return defaultValue
      }
    },

    remove(key: string): void {
      try {
        sessionStorage.removeItem(key)
      } catch (error) {
        console.error('sessionStorage删除失败:', error)
      }
    },

    clear(): void {
      try {
        sessionStorage.clear()
      } catch (error) {
        console.error('sessionStorage清空失败:', error)
      }
    },
  },
}

/**
 * 设备工具
 */
export const deviceUtils = {
  // 检测移动设备
  isMobile(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    )
  },

  // 检测iOS
  isIOS(): boolean {
    return /iPad|iPhone|iPod/.test(navigator.userAgent)
  },

  // 检测Android
  isAndroid(): boolean {
    return /Android/i.test(navigator.userAgent)
  },

  // 检测微信浏览器
  isWeChat(): boolean {
    return /MicroMessenger/i.test(navigator.userAgent)
  },

  // 获取设备信息
  getDeviceInfo(): {
    userAgent: string
    isMobile: boolean
    isIOS: boolean
    isAndroid: boolean
    isWeChat: boolean
    screenWidth: number
    screenHeight: number
  } {
    return {
      userAgent: navigator.userAgent,
      isMobile: this.isMobile(),
      isIOS: this.isIOS(),
      isAndroid: this.isAndroid(),
      isWeChat: this.isWeChat(),
      screenWidth: window.screen.width,
      screenHeight: window.screen.height,
    }
  },
}

/**
 * 文件工具
 */
export const fileUtils = {
  // 读取文件为Base64
  readAsBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  },

  // 读取文件为文本
  readAsText(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = reject
      reader.readAsText(file, 'UTF-8')
    })
  },

  // 下载文件
  download(data: string | Blob, filename: string, type?: string): void {
    const blob = typeof data === 'string' ? new Blob([data], { type: type || 'text/plain' }) : data

    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  },

  // 压缩图片
  compressImage(file: File, quality = 0.8): Promise<Blob> {
    return new Promise(resolve => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      const img = new Image()

      img.onload = () => {
        canvas.width = img.width
        canvas.height = img.height
        ctx.drawImage(img, 0, 0)
        canvas.toBlob(resolve as any, 'image/jpeg', quality)
      }

      img.src = URL.createObjectURL(file)
    })
  },
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }

    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean

  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

/**
 * 延迟函数
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 格式化数字
 */
export function formatNumber(num: number, precision = 2): string {
  if (num >= 1e9) return (num / 1e9).toFixed(precision) + 'B'
  if (num >= 1e6) return (num / 1e6).toFixed(precision) + 'M'
  if (num >= 1e3) return (num / 1e3).toFixed(precision) + 'K'
  return num.toString()
}

/**
 * 生成UUID
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
