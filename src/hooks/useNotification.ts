import { NotificationType } from '@/components/Notification'
import { useCallback, useState } from 'react'

export interface NotificationState {
  type: NotificationType
  title?: string
  message: string
  isVisible: boolean
}

/**
 * 通知管理Hook
 * 简化通知的状态管理
 */
export const useNotification = () => {
  const [notification, setNotification] = useState<NotificationState | null>(null)

  // 显示通知
  const showNotification = useCallback((
    type: NotificationType,
    message: string,
    title?: string
  ) => {
    console.log(`🔔 显示${type}通知: ${message}`)
    setNotification({
      type,
      title,
      message,
      isVisible: true
    })
  }, [])

  // 显示成功通知
  const showSuccess = useCallback((message: string, title?: string) => {
    showNotification('success', message, title)
  }, [showNotification])

  // 显示错误通知
  const showError = useCallback((message: string, title?: string) => {
    showNotification('error', message, title)
  }, [showNotification])

  // 显示警告通知
  const showWarning = useCallback((message: string, title?: string) => {
    showNotification('warning', message, title)
  }, [showNotification])

  // 显示信息通知
  const showInfo = useCallback((message: string, title?: string) => {
    showNotification('info', message, title)
  }, [showNotification])

  // 隐藏通知
  const hideNotification = useCallback(() => {
    console.log('🔔 隐藏通知')
    setNotification(prev => prev ? { ...prev, isVisible: false } : null)
    
    // 延迟清理状态，等待动画完成
    setTimeout(() => {
      setNotification(null)
    }, 300)
  }, [])

  return {
    notification,
    showNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    hideNotification
  }
} 