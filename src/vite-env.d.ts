/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_DIFY_BASE_URL: string
  readonly VITE_DIFY_API_KEY: string
  readonly VITE_DIFY_REPORT_APP_ID: string
  readonly VITE_DIFY_DOCTOR_APP_ID: string
  readonly VITE_APP_NAME: string
  readonly VITE_APP_VERSION: string
  readonly VITE_API_TIMEOUT: string
  readonly VITE_API_RETRY_COUNT: string
  readonly VITE_UPLOAD_MAX_SIZE: string
  readonly VITE_UPLOAD_MAX_FILES: string
  readonly VITE_DEV_LOG_LEVEL: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
