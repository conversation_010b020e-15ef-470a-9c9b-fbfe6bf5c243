import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import Alert from '../Alert'

describe('Alert Component', () => {
  it('应该正确渲染错误提示', () => {
    const mockOnClose = vi.fn()
    
    render(
      <Alert
        type="error"
        message="这是一个错误消息"
        title="错误"
        isVisible={true}
        onClose={mockOnClose}
      />
    )

    expect(screen.getByText('错误')).toBeInTheDocument()
    expect(screen.getByText('这是一个错误消息')).toBeInTheDocument()
  })

  it('应该正确渲染警告提示', () => {
    const mockOnClose = vi.fn()
    
    render(
      <Alert
        type="warning"
        message="这是一个警告消息"
        isVisible={true}
        onClose={mockOnClose}
      />
    )

    expect(screen.getByText('这是一个警告消息')).toBeInTheDocument()
  })

  it('应该正确渲染成功提示', () => {
    const mockOnClose = vi.fn()
    
    render(
      <Alert
        type="success"
        message="操作成功"
        isVisible={true}
        onClose={mockOnClose}
      />
    )

    expect(screen.getByText('操作成功')).toBeInTheDocument()
  })

  it('点击关闭按钮应该调用onClose', () => {
    const mockOnClose = vi.fn()
    
    render(
      <Alert
        type="info"
        message="信息提示"
        isVisible={true}
        onClose={mockOnClose}
        closable={true}
      />
    )

    const closeButton = screen.getByTitle('关闭')
    fireEvent.click(closeButton)
    
    expect(mockOnClose).toHaveBeenCalledTimes(1)
  })

  it('应该支持自动关闭', async () => {
    const mockOnClose = vi.fn()
    
    render(
      <Alert
        type="info"
        message="自动关闭测试"
        isVisible={true}
        onClose={mockOnClose}
        autoClose={true}
        duration={100}
      />
    )

    await waitFor(() => {
      expect(mockOnClose).toHaveBeenCalledTimes(1)
    }, { timeout: 200 })
  })

  it('不可见时不应该渲染', () => {
    const mockOnClose = vi.fn()
    
    render(
      <Alert
        type="error"
        message="不可见的消息"
        isVisible={false}
        onClose={mockOnClose}
      />
    )

    expect(screen.queryByText('不可见的消息')).not.toBeInTheDocument()
  })
})
