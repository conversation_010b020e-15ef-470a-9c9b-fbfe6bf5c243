import { AnimatePresence, motion } from 'framer-motion'
import { AlertCircle, AlertTriangle, CheckCircle, Info, X } from 'lucide-react'
import React, { useEffect } from 'react'

// 提示类型定义
export type AlertType = 'success' | 'error' | 'warning' | 'info'

export interface AlertProps {
  /** 提示类型 */
  type: AlertType
  /** 提示消息内容 */
  message: string
  /** 提示标题（可选） */
  title?: string
  /** 是否显示 */
  isVisible: boolean
  /** 关闭回调 */
  onClose: () => void
  /** 是否自动关闭 */
  autoClose?: boolean
  /** 自动关闭延迟时间（毫秒） */
  duration?: number
  /** 显示位置 */
  position?: 'top' | 'bottom'
  /** 是否可手动关闭 */
  closable?: boolean
  /** 自定义样式类名 */
  className?: string
}

/**
 * 通用提示组件
 * 支持成功、错误、警告、信息四种类型
 * 基于AI健康咨询页面的提示语样式设计
 */
const Alert: React.FC<AlertProps> = ({
  type,
  message,
  title,
  isVisible,
  onClose,
  autoClose = true,
  duration = 5000,
  position = 'top',
  closable = true,
  className = ''
}) => {
  // 自动关闭逻辑
  useEffect(() => {
    if (isVisible && autoClose && duration > 0) {
      const timer = setTimeout(() => {
        onClose()
      }, duration)

      return () => clearTimeout(timer)
    }
  }, [isVisible, autoClose, duration, onClose])

  // 获取样式配置
  const getAlertConfig = () => {
    switch (type) {
      case 'success':
        return {
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          textColor: 'text-green-800',
          titleColor: 'text-green-800',
          iconColor: 'text-green-600',
          icon: CheckCircle
        }
      case 'error':
        return {
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-700',
          titleColor: 'text-red-800',
          iconColor: 'text-red-600',
          icon: AlertCircle
        }
      case 'warning':
        return {
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-800',
          titleColor: 'text-yellow-800',
          iconColor: 'text-yellow-600',
          icon: AlertTriangle
        }
      case 'info':
        return {
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-700',
          titleColor: 'text-blue-800',
          iconColor: 'text-blue-600',
          icon: Info
        }
    }
  }

  const config = getAlertConfig()
  const Icon = config.icon

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ 
            opacity: 0, 
            y: position === 'top' ? -50 : 50,
            scale: 0.95
          }}
          animate={{ 
            opacity: 1, 
            y: 0,
            scale: 1
          }}
          exit={{ 
            opacity: 0, 
            y: position === 'top' ? -50 : 50,
            scale: 0.95
          }}
          transition={{ 
            type: "spring", 
            stiffness: 500, 
            damping: 30,
            duration: 0.3
          }}
          className={`
            fixed ${position === 'top' ? 'top-4' : 'bottom-4'} 
            left-4 right-4 
            sm:left-1/2 sm:right-auto sm:-translate-x-1/2 sm:w-auto sm:min-w-[400px] sm:max-w-[600px]
            z-50
            ${className}
          `}
        >
          <div className={`
            ${config.bgColor} ${config.borderColor}
            border rounded-xl p-4 shadow-lg backdrop-blur-sm
          `}>
            <div className="flex items-start space-x-3">
              {/* 图标 */}
              <Icon className={`w-5 h-5 ${config.iconColor} mt-0.5 flex-shrink-0`} />
              
              {/* 内容区域 */}
              <div className="flex-1 min-w-0">
                {title && (
                  <p className={`font-medium mb-1 text-sm ${config.titleColor}`}>{title}</p>
                )}
                <p className={`text-sm leading-relaxed ${config.textColor}`}>{message}</p>
              </div>

              {/* 关闭按钮 */}
              {closable && (
                <button
                  onClick={onClose}
                  className={`${config.iconColor} hover:opacity-70 transition-opacity flex-shrink-0 mt-0.5`}
                  title="关闭"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default Alert
