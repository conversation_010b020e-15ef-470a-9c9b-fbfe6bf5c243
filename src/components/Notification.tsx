import { AnimatePresence, motion } from 'framer-motion'
import { AlertCircle, AlertTriangle, CheckCircle, X } from 'lucide-react'
import React, { useEffect } from 'react'

// 通知类型定义
export type NotificationType = 'success' | 'error' | 'warning' | 'info'

export interface NotificationProps {
  type: NotificationType
  title?: string
  message: string
  isVisible: boolean
  onClose: () => void
  autoClose?: boolean
  duration?: number
  position?: 'top' | 'bottom'
}

/**
 * 通知组件
 * 支持顶部/底部下拉显示，兼容不同类型的提示
 */
const Notification: React.FC<NotificationProps> = ({
  type,
  title,
  message,
  isVisible,
  onClose,
  autoClose = true,
  duration = 5000,
  position = 'top'
}) => {
  // 自动关闭
  useEffect(() => {
    if (isVisible && autoClose) {
      const timer = setTimeout(() => {
        onClose()
      }, duration)

      return () => clearTimeout(timer)
    }
  }, [isVisible, autoClose, duration, onClose])

  // 获取样式配置
  const getNotificationConfig = () => {
    switch (type) {
      case 'success':
        return {
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          textColor: 'text-green-800',
          iconColor: 'text-green-600',
          icon: CheckCircle
        }
      case 'error':
        return {
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
          iconColor: 'text-red-600',
          icon: AlertCircle
        }
      case 'warning':
        return {
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-800',
          iconColor: 'text-yellow-600',
          icon: AlertTriangle
        }
      case 'info':
        return {
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-800',
          iconColor: 'text-blue-600',
          icon: AlertCircle
        }
    }
  }

  const config = getNotificationConfig()
  const Icon = config.icon

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ 
            opacity: 0, 
            y: position === 'top' ? -100 : 100,
            scale: 0.95
          }}
          animate={{ 
            opacity: 1, 
            y: 0,
            scale: 1
          }}
          exit={{ 
            opacity: 0, 
            y: position === 'top' ? -100 : 100,
            scale: 0.95
          }}
          transition={{ 
            type: "spring", 
            stiffness: 500, 
            damping: 30,
            duration: 0.3
          }}
          className={`
            fixed ${position === 'top' ? 'top-4' : 'bottom-4'} 
            left-4 right-4 
            sm:left-1/2 sm:right-auto sm:-translate-x-1/2 sm:w-auto sm:min-w-[400px] sm:max-w-[600px]
            z-50
          `}
        >
          <div className={`
            ${config.bgColor} ${config.borderColor} ${config.textColor}
            border rounded-xl p-4 shadow-lg backdrop-blur-sm
            flex items-start space-x-3
          `}>
            {/* 图标 */}
            <Icon className={`w-5 h-5 ${config.iconColor} mt-0.5 flex-shrink-0`} />
            
            {/* 内容 */}
            <div className="flex-1 min-w-0">
              {title && (
                <p className="font-medium mb-1 text-sm">{title}</p>
              )}
              <p className="text-sm leading-relaxed">{message}</p>
            </div>

            {/* 关闭按钮 */}
            <button
              onClick={onClose}
              className={`
                ${config.iconColor} hover:opacity-70 
                transition-opacity flex-shrink-0 mt-0.5
              `}
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default Notification 