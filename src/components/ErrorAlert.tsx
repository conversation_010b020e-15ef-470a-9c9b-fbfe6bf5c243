import { AnimatePresence, motion } from 'framer-motion'
import { AlertCircle, X } from 'lucide-react'
import React, { useEffect } from 'react'

export interface ErrorAlertProps {
  /** 错误消息内容 */
  message: string
  /** 错误标题（可选） */
  title?: string
  /** 是否显示 */
  isVisible: boolean
  /** 关闭回调 */
  onClose: () => void
  /** 是否自动关闭 */
  autoClose?: boolean
  /** 自动关闭延迟时间（毫秒） */
  duration?: number
  /** 显示位置 */
  position?: 'top' | 'bottom'
  /** 是否可手动关闭 */
  closable?: boolean
  /** 自定义样式类名 */
  className?: string
}

/**
 * 通用错误提示组件
 * 基于AI健康咨询页面的黄色提示语样式设计
 * 支持顶部/底部显示，自动关闭等功能
 */
const ErrorAlert: React.FC<ErrorAlertProps> = ({
  message,
  title,
  isVisible,
  onClose,
  autoClose = true,
  duration = 5000,
  position = 'top',
  closable = true,
  className = ''
}) => {
  // 自动关闭逻辑
  useEffect(() => {
    if (isVisible && autoClose && duration > 0) {
      const timer = setTimeout(() => {
        onClose()
      }, duration)

      return () => clearTimeout(timer)
    }
  }, [isVisible, autoClose, duration, onClose])

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ 
            opacity: 0, 
            y: position === 'top' ? -50 : 50,
            scale: 0.95
          }}
          animate={{ 
            opacity: 1, 
            y: 0,
            scale: 1
          }}
          exit={{ 
            opacity: 0, 
            y: position === 'top' ? -50 : 50,
            scale: 0.95
          }}
          transition={{ 
            type: "spring", 
            stiffness: 500, 
            damping: 30,
            duration: 0.3
          }}
          className={`
            fixed ${position === 'top' ? 'top-4' : 'bottom-4'} 
            left-4 right-4 
            sm:left-1/2 sm:right-auto sm:-translate-x-1/2 sm:w-auto sm:min-w-[400px] sm:max-w-[600px]
            z-50
            ${className}
          `}
        >
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 shadow-lg backdrop-blur-sm">
            <div className="flex items-start space-x-3">
              {/* 错误图标 */}
              <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
              
              {/* 内容区域 */}
              <div className="flex-1 min-w-0">
                {title && (
                  <p className="font-medium mb-1 text-sm text-red-800">{title}</p>
                )}
                <p className="text-sm leading-relaxed text-red-700">{message}</p>
              </div>

              {/* 关闭按钮 */}
              {closable && (
                <button
                  onClick={onClose}
                  className="text-red-600 hover:text-red-800 transition-colors flex-shrink-0 mt-0.5"
                  title="关闭"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default ErrorAlert
