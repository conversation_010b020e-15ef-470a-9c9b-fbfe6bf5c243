import React from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom'
import App from './App.tsx'
import './index.css'

// 全局错误处理
window.addEventListener('error', event => {
  console.error('全局错误捕获:', event.error)
})

window.addEventListener('unhandledrejection', event => {
  console.error('未处理的Promise拒绝:', event.reason)
})

// 开发环境下启用性能监控
if (process.env.NODE_ENV === 'development') {
  console.log('🚀 个人健康助理应用启动中...')
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </React.StrictMode>
)
