import { AnimatePresence, motion } from 'framer-motion'
import {
  AlertCircle,
  Camera,
  CheckCircle,
  FileText,
  Loader2,
  RotateCcw,
  Send,
  Upload
} from 'lucide-react'
import React, { useRef, useState } from 'react'
import { useSearchParams } from 'react-router-dom'

// 组件和Hook
import Notification from '@/components/Notification'
import { useNotification } from '@/hooks/useNotification'

// 工具和API
import { config, configUtils } from '@/config'
import { apiUtils, difyClient } from '@/utils/api'

/**
 * 报告解读页面
 * 支持拍照和上传两种方式解读体检报告
 */
const ReportAnalysis: React.FC = () => {
  const [searchParams] = useSearchParams()
  const mode = searchParams.get('mode') || 'camera' // camera | upload

  // 状态管理
  const [step, setStep] = useState<'input' | 'preview' | 'analysis' | 'result'>('input')
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [analysis, setAnalysis] = useState<string>('')
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [conversationId, setConversationId] = useState<string>('')
  const [error, setError] = useState<string>('')

  // 通知系统
  const { notification, showSuccess, hideNotification } = useNotification()

  // 引用
  const fileInputRef = useRef<HTMLInputElement>(null)
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    console.log(`📁 选择了 ${files.length} 个文件`)

    // 验证文件
    const validFiles = files.filter(file => {
      if (!apiUtils.validateFileType(file)) {
        console.warn(`❌ 不支持的文件类型: ${file.type}`)
        return false
      }
      if (!apiUtils.validateFileSize(file)) {
        console.warn(`❌ 文件过大: ${apiUtils.formatFileSize(file.size)}`)
        return false
      }
      return true
    })

    if (validFiles.length !== files.length) {
      setError('部分文件格式不支持或文件过大，请检查后重新选择')
      return
    }

    if (validFiles.length > config.upload.maxFiles) {
      setError(`最多只能选择 ${config.upload.maxFiles} 个文件`)
      return
    }

    setSelectedFiles(validFiles)
    setError('') // 清除之前的错误

    if (validFiles.length > 0) {
      setStep('preview')
      showSuccess(`成功选择 ${validFiles.length} 个文件`)
    }
  }

  // 启动相机
  const startCamera = async () => {
    try {
      console.log('📷 启动相机')
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'environment' }, // 后置摄像头
      })

      if (videoRef.current) {
        videoRef.current.srcObject = stream
      }
      showSuccess('相机启动成功')
    } catch (error) {
      console.error('📷 相机启动失败:', error)
      setError('无法访问相机，请检查权限设置')
    }
  }

  // 拍照
  const takePhoto = () => {
    if (!videoRef.current || !canvasRef.current) return

    const video = videoRef.current
    const canvas = canvasRef.current
    const context = canvas.getContext('2d')!

    // 设置画布尺寸
    canvas.width = video.videoWidth
    canvas.height = video.videoHeight

    // 绘制当前帧
    context.drawImage(video, 0, 0)

    // 转换为文件
    canvas.toBlob(
      blob => {
        if (blob) {
          const file = new File([blob], `report_${Date.now()}.jpg`, { type: 'image/jpeg' })
          setSelectedFiles([file])
          setStep('preview')

          // 停止相机
          const stream = video.srcObject as MediaStream
          stream?.getTracks().forEach(track => track.stop())
          
          showSuccess('拍照成功，请确认后开始分析')
        }
      },
      'image/jpeg',
      0.8
    )
  }

  // 开始分析
  const startAnalysis = async () => {
    if (!configUtils.isAgentConfigured('report')) {
      setError('报告解读服务未配置，请联系管理员')
      return
    }

    setStep('analysis')
    setIsAnalyzing(true)
    setError('') // 清除之前的错误

    try {
      console.log('🔍 开始分析报告')

      // 构建分析提示
      const prompt = `
请仔细分析这份体检报告，提供以下内容：

1. **报告概述**：简要说明报告的类型和检查项目
2. **关键指标分析**：
   - 异常指标及其含义
   - 正常指标的确认
   - 临界值的说明
3. **健康评估**：
   - 整体健康状况评价
   - 风险等级评估
4. **建议措施**：
   - 生活方式调整建议
   - 复查建议
   - 是否需要进一步检查
5. **注意事项**：需要特别关注的健康问题

请用通俗易懂的语言解释，避免过多医学术语，确保普通人能够理解。
`

      // 调用Dify API
      const response = await difyClient.sendMessage(
        config.dify.reportAgent.appId,
        prompt,
        conversationId,
        selectedFiles
      )

      setAnalysis(response.answer)
      setConversationId(response.conversation_id)
      setStep('result')
      
      showSuccess('报告分析完成', '✅ 分析成功')
      console.log('✅ 报告分析完成')
    } catch (error) {
      console.error('🚨 分析失败:', error)
      const errorMessage = error instanceof Error ? error.message : '分析失败，请稍后重试'
      setError(errorMessage)
      setStep('preview')
    } finally {
      setIsAnalyzing(false)
    }
  }

  // 重新开始
  const restart = () => {
    setStep('input')
    setSelectedFiles([])
    setAnalysis('')
    setConversationId('')
    setError('') // 清除错误

    // 重置文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // 渲染文件预览
  const renderFilePreview = (file: File, index: number) => (
    <div key={index} className="relative">
      <img
        src={URL.createObjectURL(file)}
        alt={`报告 ${index + 1}`}
        className="w-full h-48 object-cover rounded-xl border border-gray-200"
      />
      <div className="absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
        {apiUtils.formatFileSize(file.size)}
      </div>
    </div>
  )

  return (
    <>
      {/* 错误提示 - 顶部显示 */}
      {error && (
        <div className="fixed top-4 left-4 right-4 sm:left-1/2 sm:right-auto sm:-translate-x-1/2 sm:w-auto sm:min-w-[400px] sm:max-w-[600px] z-50">
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 shadow-lg backdrop-blur-sm">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <p className="text-sm leading-relaxed text-red-700">{error}</p>
              </div>
              <button
                onClick={() => setError('')}
                className="text-red-600 hover:text-red-800 transition-colors flex-shrink-0 mt-0.5"
                title="关闭"
              >
                ✕
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 通知组件 */}
      {notification && (
        <Notification
          type={notification.type}
          title={notification.title}
          message={notification.message}
          isVisible={notification.isVisible}
          onClose={hideNotification}
          autoClose={true}
          duration={5000}
          position="top"
        />
      )}

      <div className="space-y-4 sm:space-y-6 pt-6">
        {/* 步骤指示器 - 响应式设计 */}
        <div className="flex items-center justify-center space-x-2 sm:space-x-3">
          {['input', 'preview', 'analysis', 'result'].map((stepName, index) => (
            <div
              key={stepName}
              className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full transition-colors ${
                step === stepName
                  ? 'bg-blue-500'
                  : ['input', 'preview', 'analysis', 'result'].indexOf(step) > index
                    ? 'bg-green-500'
                    : 'bg-gray-300'
              }`}
            />
          ))}
        </div>

        <AnimatePresence mode="wait">
          {/* 输入阶段 */}
          {step === 'input' && (
            <motion.div
              key="input"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <div className="text-center">
                <h2 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 mb-2">
                  {mode === 'camera' ? '拍照解读报告' : '上传报告图片'}
                </h2>
                <p className="text-sm sm:text-base text-gray-600">
                  {mode === 'camera' ? '请对准体检报告进行拍照' : '支持JPG、PNG格式，最大10MB'}
                </p>
              </div>

              {mode === 'camera' ? (
                // 相机模式
                <div className="space-y-4">
                  <div className="relative bg-black rounded-2xl overflow-hidden aspect-[4/3]">
                    <video
                      ref={videoRef}
                      autoPlay
                      playsInline
                      muted
                      className="w-full h-full object-cover"
                    />
                    <canvas ref={canvasRef} className="hidden" />
                  </div>

                  <div className="flex space-x-4">
                    <button onClick={startCamera} className="flex-1 btn-secondary">
                      <Camera className="w-5 h-5 mr-2" />
                      启动相机
                    </button>
                    <button
                      onClick={takePhoto}
                      disabled={!videoRef.current?.srcObject}
                      className="flex-1 btn-primary disabled:opacity-50"
                    >
                      拍照
                    </button>
                  </div>
                </div>
              ) : (
                // 上传模式
                <div className="space-y-4">
                  <div
                    onClick={() => fileInputRef.current?.click()}
                    className="border-2 border-dashed border-gray-300 rounded-2xl p-6 sm:p-8 lg:p-12 text-center cursor-pointer hover:border-blue-400 transition-colors"
                  >
                    <Upload className="w-10 h-10 sm:w-12 sm:h-12 lg:w-16 lg:h-16 text-gray-400 mx-auto mb-4" />
                    <p className="text-sm sm:text-base text-gray-600 mb-2">点击选择报告图片</p>
                    <p className="text-xs sm:text-sm text-gray-500">
                      支持 JPG、PNG 格式，最大 {config.upload.maxSize}MB
                    </p>
                  </div>

                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept={config.upload.allowedTypes.join(',')}
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                </div>
              )}
            </motion.div>
          )}

          {/* 预览阶段 */}
          {step === 'preview' && (
            <motion.div
              key="preview"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <div className="text-center">
                <h2 className="text-xl font-bold text-gray-900 mb-2">预览报告</h2>
                <p className="text-gray-600">确认图片清晰后开始分析</p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                {selectedFiles.map((file, index) => renderFilePreview(file, index))}
              </div>

              <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
                <button onClick={restart} className="flex-1 btn-secondary">
                  <RotateCcw className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                  重新选择
                </button>
                <button
                  onClick={startAnalysis}
                  disabled={isAnalyzing}
                  className="flex-1 btn-primary disabled:opacity-50"
                >
                  {isAnalyzing ? (
                    <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 mr-2 animate-spin" />
                  ) : (
                    <Send className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                  )}
                  {isAnalyzing ? '分析中...' : '开始分析'}
                </button>
              </div>
            </motion.div>
          )}

          {/* 分析阶段 */}
          {step === 'analysis' && (
            <motion.div
              key="analysis"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Loader2 className="w-8 h-8 text-blue-600 animate-spin" />
                </div>
                <h2 className="text-xl font-bold text-gray-900 mb-2">AI正在分析</h2>
                <p className="text-gray-600">请稍等，正在仔细解读您的体检报告...</p>
              </div>

              <div className="bg-blue-50 rounded-2xl p-6 space-y-3">
                <div className="flex items-center text-blue-700">
                  <CheckCircle className="w-5 h-5 mr-2" />
                  <span className="text-sm">报告上传成功</span>
                </div>
                <div className="flex items-center text-blue-700">
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  <span className="text-sm">智能分析中...</span>
                </div>
              </div>
            </motion.div>
          )}

          {/* 结果阶段 */}
          {step === 'result' && (
            <motion.div
              key="result"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <h2 className="text-xl font-bold text-gray-900 mb-2">分析完成</h2>
                <p className="text-gray-600">以下是您的报告解读结果</p>
              </div>

              {/* 分析结果 */}
              <div className="bg-white rounded-2xl border border-gray-200 p-6">
                <div className="prose prose-sm max-w-none">
                  <div className="whitespace-pre-wrap text-gray-800 leading-relaxed">{analysis}</div>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex space-x-4">
                <button onClick={restart} className="flex-1 btn-secondary">
                  <FileText className="w-5 h-5 mr-2" />
                  分析新报告
                </button>
                <button
                  onClick={() => {
                    // 保存结果到本地存储
                    const reportData = {
                      files: selectedFiles.map(f => f.name),
                      analysis,
                      timestamp: Date.now(),
                    }
                    const history = JSON.parse(localStorage.getItem('report_history') || '[]')
                    history.unshift(reportData)
                    localStorage.setItem('report_history', JSON.stringify(history.slice(0, 10)))

                    console.log('💾 报告结果已保存到本地')
                    showSuccess('报告结果已保存到本地', '保存成功')
                  }}
                  className="flex-1 btn-primary"
                >
                  保存结果
                </button>
              </div>

              {/* 免责声明 */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
                <div className="flex items-start space-x-3">
                  <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
                  <div className="text-sm text-yellow-800">
                    <p className="font-medium mb-1">重要提示</p>
                    <p>此解读结果仅供参考，不能替代专业医生的诊断。如有疑问请及时咨询专业医师。</p>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </>
  )
}

export default ReportAnalysis
