import { motion } from 'framer-motion'
import {
    Camera,
    Heart,
    MessageCircle,
    Shield,
    Sparkles,
    Stethoscope,
    TrendingUp,
} from 'lucide-react'
import React from 'react'
import { useNavigate } from 'react-router-dom'

/**
 * 首页组件
 * 展示所有功能入口，支持响应式设计
 */
const Home: React.FC = () => {
  const navigate = useNavigate()

  // 功能配置
  const features = [
    // 报告解读智能体区域
    {
      id: 'photo-analysis',
      title: '拍照解读',
      description: '拍摄体检报告，AI智能分析',
      icon: Camera,
      color: 'health-camera',
      bgColor: 'bg-blue-50',
      route: '/report-analysis',
      category: 'report',
    },
    // 文件解读智能体区域
    {
      id: 'report-analysis',
      title: '文件解读',
      description: '上传体检报告，AI智能分析',
      icon: TrendingUp, // 使用趋势图标表示文件分析
      color: 'health-report', // 健康报告相关的颜色标识
      bgColor: 'bg-green-50', // 使用绿色背景区分于拍照解读
      route: '/report-analysis',
      category: 'report', // 报告类别功能
    },
    // 健康咨询智能体区域
    {
      id: 'ai-consultation',
      title: 'AI健康咨询',
      description: '智能医生，24小时在线',
      icon: MessageCircle,
      color: 'health-chat',
      bgColor: 'bg-purple-50',
      route: '/consultation',
      category: 'doctor',
    },
    {
      id: 'symptom-assessment',
      title: '症状自评',
      description: '症状记录与智能评估',
      icon: Stethoscope,
      color: 'health-assess',
      bgColor: 'bg-orange-50',
      route: '/assessment',
      category: 'doctor',
    },
    {
      id: 'trend-analysis',
      title: '趋势分析',
      description: '健康数据趋势分析',
      icon: TrendingUp,
      color: 'health-trend',
      bgColor: 'bg-indigo-50',
      route: '/trend',
      category: 'doctor',
    },
  ]

  // 导航到功能页面
  const navigateToFeature = (route: string, title: string) => {
    console.log(`🔗 导航到功能页面: ${title} (${route})`)
    navigate(route)
  }

  // 分组功能
  const reportFeatures = features.filter(f => f.category === 'report')
  const doctorFeatures = features.filter(f => f.category === 'doctor')

  return (
    <div className="space-y-8 pt-6">
      {/* 欢迎横幅 - 响应式设计 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-8"
      >
        <div className="inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mb-4 shadow-lg">
          <Heart className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
        </div>
        <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-2">个人健康助理</h1>
        <p className="text-sm sm:text-base text-gray-600 max-w-sm sm:max-w-md lg:max-w-lg mx-auto">
          智能解读体检报告，提供专业健康咨询，让健康管理更简单
        </p>
      </motion.div>

      {/* 报告解读智能体区域 - 响应式网格 */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="space-y-4"
      >
        <div className="flex items-center space-x-2 mb-4">
          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <Shield className="w-5 h-5 text-blue-600" />
          </div>
          <h2 className="text-lg sm:text-xl font-semibold text-gray-900">报告解读智能体</h2>
        </div>

        {/* 响应式网格：手机2列，平板2列，桌面2列 */}
        <div className="space-y-4">
          {reportFeatures.map((feature, index) => {
            const Icon = feature.icon
            return (
              <motion.div
                key={feature.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2 + index * 0.1 }}
                onClick={() => navigateToFeature(feature.route, feature.title)}
                className={`feature-card p-4 sm:p-6 ${feature.bgColor} cursor-pointer hover:scale-105 transition-transform`}
              >
                <div className="flex items-center space-x-4">
                  <div className={`icon-container bg-white flex-shrink-0`}>
                    <Icon className={`w-6 h-6 sm:w-8 sm:h-8 text-${feature.color}`} />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 mb-1 sm:mb-2 text-sm sm:text-base">{feature.title}</h3>
                    <p className="text-xs sm:text-sm text-gray-600 leading-relaxed">{feature.description}</p>
                  </div>
                </div>
              </motion.div>
            )
          })}
        </div>
      </motion.section>

      {/* 全科医生智能体区域 - 响应式布局 */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="space-y-4"
      >
        <div className="flex items-center space-x-2 mb-4">
          <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
            <Sparkles className="w-5 h-5 text-purple-600" />
          </div>
          <h2 className="text-lg sm:text-xl font-semibold text-gray-900">全科医生智能体</h2>
        </div>

        <div className="space-y-4 lg:space-y-6">
          {/* AI健康咨询 - 大卡片，桌面端可以更宽 */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.4 }}
            onClick={() => navigateToFeature('/consultation', 'AI健康咨询')}
            className="feature-card p-4 sm:p-6 bg-purple-50 cursor-pointer hover:scale-105 transition-transform"
          >
            <div className="flex items-center space-x-4">
              <div className="icon-container bg-white flex-shrink-0">
                <MessageCircle className="w-6 h-6 sm:w-8 sm:h-8 text-health-chat" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 mb-1 text-sm sm:text-base">AI健康咨询</h3>
                <p className="text-xs sm:text-sm text-gray-600">智能医生24小时在线，随时为您解答健康问题</p>
              </div>
            </div>
          </motion.div>

          {/* 症状自评和趋势分析 - 响应式网格：手机也是2列 */}
          <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
            {doctorFeatures.slice(1).map((feature, index) => {
              const Icon = feature.icon
              return (
                <motion.div
                  key={feature.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5 + index * 0.1 }}
                  onClick={() => navigateToFeature(feature.route, feature.title)}
                  className={`feature-card p-4 sm:p-6 ${feature.bgColor} cursor-pointer hover:scale-105 transition-transform`}
                >
                  <div className="icon-container mb-3 sm:mb-4 bg-white">
                    <Icon className={`w-6 h-6 sm:w-8 sm:h-8 text-${feature.color}`} />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-1 sm:mb-2 text-sm sm:text-base">{feature.title}</h3>
                  <p className="text-xs sm:text-sm text-gray-600 leading-relaxed">{feature.description}</p>
                </motion.div>
              )
            })}
          </div>
        </div>
      </motion.section>

      {/* 使用提示 - 响应式设计 */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.6 }}
        className="bg-blue-50 rounded-2xl p-4 sm:p-6 border border-blue-100"
      >
        <div className="flex items-start space-x-3">
          <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-1 flex-shrink-0">
            <span className="text-blue-600 text-sm font-bold">💡</span>
          </div>
          <div>
            <h4 className="font-medium text-blue-900 mb-2 text-sm sm:text-base">使用提示</h4>
            <ul className="text-xs sm:text-sm text-blue-700 space-y-1">
              <li>• 体检报告解读：支持拍照和上传图片两种方式</li>
              <li>• AI健康咨询：可进行多轮对话，获得专业建议</li>
              <li>• 数据隐私：所有健康数据仅在本地处理，确保隐私安全</li>
            </ul>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default Home
