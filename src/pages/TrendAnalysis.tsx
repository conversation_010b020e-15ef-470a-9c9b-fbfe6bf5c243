import { AnimatePresence, motion } from 'framer-motion'
import {
  Activity,
  AlertCircle,
  BarChart3,
  Calendar,
  CheckCircle,
  Clock,
  Droplets,
  Heart,
  Moon,
  Plus,
  Scale,
  Target,
  Thermometer,
  Trash2,
  TrendingDown,
  TrendingUp,
} from 'lucide-react'
import React, { useEffect, useState } from 'react'

// 工具和API
import { arrayUtils, dateUtils, storageUtils } from '@/utils'

// 健康数据类型
interface HealthMetric {
  id: string
  type:
    | 'blood_pressure'
    | 'heart_rate'
    | 'temperature'
    | 'weight'
    | 'blood_sugar'
    | 'sleep'
    | 'exercise'
  name: string
  value: number | string
  unit: string
  date: string
  note?: string
  normalRange?: { min: number; max: number }
}

interface TrendData {
  type: string
  data: { date: string; value: number }[]
  trend: 'up' | 'down' | 'stable'
  changePercent: number
}

/**
 * 趋势分析页面
 * 健康数据记录和趋势分析
 */
const TrendAnalysis: React.FC = () => {
  // 状态管理
  const [view, setView] = useState<'overview' | 'add' | 'detail'>('overview')
  const [healthMetrics, setHealthMetrics] = useState<HealthMetric[]>([])
  const [trends, setTrends] = useState<TrendData[]>([])

  // 健康指标配置
  const metricTypes = [
    {
      type: 'blood_pressure' as const,
      name: '血压',
      icon: Heart,
      unit: 'mmHg',
      color: 'text-red-500',
      bgColor: 'bg-red-50',
      normalRange: { min: 90, max: 140 },
      placeholder: '收缩压/舒张压，如：120/80',
    },
    {
      type: 'heart_rate' as const,
      name: '心率',
      icon: Activity,
      unit: 'bpm',
      color: 'text-pink-500',
      bgColor: 'bg-pink-50',
      normalRange: { min: 60, max: 100 },
      placeholder: '每分钟心跳次数',
    },
    {
      type: 'temperature' as const,
      name: '体温',
      icon: Thermometer,
      unit: '°C',
      color: 'text-orange-500',
      bgColor: 'bg-orange-50',
      normalRange: { min: 36.0, max: 37.5 },
      placeholder: '体温，如：36.5',
    },
    {
      type: 'weight' as const,
      name: '体重',
      icon: Scale,
      unit: 'kg',
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
      normalRange: { min: 45, max: 100 },
      placeholder: '体重，如：65.5',
    },
    {
      type: 'blood_sugar' as const,
      name: '血糖',
      icon: Droplets,
      unit: 'mmol/L',
      color: 'text-green-500',
      bgColor: 'bg-green-50',
      normalRange: { min: 3.9, max: 6.1 },
      placeholder: '血糖值，如：5.5',
    },
    {
      type: 'sleep' as const,
      name: '睡眠',
      icon: Moon,
      unit: '小时',
      color: 'text-purple-500',
      bgColor: 'bg-purple-50',
      normalRange: { min: 7, max: 9 },
      placeholder: '睡眠时长，如：8',
    },
    {
      type: 'exercise' as const,
      name: '运动',
      icon: Clock,
      unit: '分钟',
      color: 'text-indigo-500',
      bgColor: 'bg-indigo-50',
      normalRange: { min: 30, max: 120 },
      placeholder: '运动时长，如：45',
    },
  ]

  // 加载数据
  useEffect(() => {
    console.log('📊 加载健康数据')
    const savedMetrics = storageUtils.local.get<HealthMetric[]>('health_metrics') || []
    setHealthMetrics(savedMetrics)
    calculateTrends(savedMetrics)
  }, [])

  // 计算趋势
  const calculateTrends = (metrics: HealthMetric[]) => {
    const trendData: TrendData[] = []

    metricTypes.forEach(metricType => {
      const typeMetrics = metrics
        .filter(m => m.type === metricType.type)
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

      if (typeMetrics.length >= 2) {
        const recentData = typeMetrics.slice(-7) // 最近7条记录
        const data = recentData.map(m => ({
          date: m.date,
          value: typeof m.value === 'string' ? parseFloat(m.value) || 0 : m.value,
        }))

        // 计算趋势
        const firstValue = data[0].value
        const lastValue = data[data.length - 1].value
        const changePercent = firstValue !== 0 ? ((lastValue - firstValue) / firstValue) * 100 : 0

        let trend: 'up' | 'down' | 'stable' = 'stable'
        if (Math.abs(changePercent) > 5) {
          trend = changePercent > 0 ? 'up' : 'down'
        }

        trendData.push({
          type: metricType.type,
          data,
          trend,
          changePercent,
        })
      }
    })

    setTrends(trendData)
  }

  // 添加健康数据
  const addHealthMetric = (metric: Omit<HealthMetric, 'id'>) => {
    const newMetric: HealthMetric = {
      ...metric,
      id: `metric_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    }

    const updatedMetrics = [...healthMetrics, newMetric]
    setHealthMetrics(updatedMetrics)
    storageUtils.local.set('health_metrics', updatedMetrics)
    calculateTrends(updatedMetrics)

    console.log('➕ 添加健康数据:', newMetric.name, newMetric.value)
  }

  // 删除健康数据
  const deleteHealthMetric = (metricId: string) => {
    const updatedMetrics = healthMetrics.filter(m => m.id !== metricId)
    setHealthMetrics(updatedMetrics)
    storageUtils.local.set('health_metrics', updatedMetrics)
    calculateTrends(updatedMetrics)

    console.log('🗑️ 删除健康数据:', metricId)
  }

  // 获取指标状态
  const getMetricStatus = (metric: HealthMetric) => {
    const metricConfig = metricTypes.find(t => t.type === metric.type)
    if (!metricConfig?.normalRange) return 'normal'

    const value = typeof metric.value === 'string' ? parseFloat(metric.value) || 0 : metric.value

    if (value < metricConfig.normalRange.min) return 'low'
    if (value > metricConfig.normalRange.max) return 'high'
    return 'normal'
  }

  // 渲染概览视图
  const renderOverview = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      {/* 标题和添加按钮 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-gray-900">健康趋势</h2>
          <p className="text-gray-600">记录和分析您的健康指标变化</p>
        </div>
        <button
          onClick={() => setView('add')}
          className="w-12 h-12 bg-blue-500 text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors"
        >
          <Plus className="w-6 h-6" />
        </button>
      </div>

      {/* 趋势概览卡片 */}
      {trends.length > 0 ? (
        <div className="grid gap-4">
          {trends.map(trend => {
            const metricConfig = metricTypes.find(t => t.type === trend.type)!
            const Icon = metricConfig.icon
            const latestValue = trend.data[trend.data.length - 1]?.value || 0

            return (
              <div
                key={trend.type}
                className={`${metricConfig.bgColor} rounded-2xl p-4 border border-gray-100`}
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-white rounded-xl flex items-center justify-center">
                      <Icon className={`w-6 h-6 ${metricConfig.color}`} />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{metricConfig.name}</h3>
                      <p className="text-sm text-gray-600">
                        {latestValue} {metricConfig.unit}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {trend.trend === 'up' && <TrendingUp className="w-5 h-5 text-green-500" />}
                    {trend.trend === 'down' && <TrendingDown className="w-5 h-5 text-red-500" />}
                    {trend.trend === 'stable' && <div className="w-5 h-1 bg-gray-400 rounded" />}
                    <span
                      className={`text-sm font-medium ${
                        trend.trend === 'up'
                          ? 'text-green-600'
                          : trend.trend === 'down'
                            ? 'text-red-600'
                            : 'text-gray-600'
                      }`}
                    >
                      {Math.abs(trend.changePercent).toFixed(1)}%
                    </span>
                  </div>
                </div>

                {/* 简化趋势图 */}
                <div className="h-12 bg-white/50 rounded-lg p-2">
                  <div className="flex items-end space-x-1 h-full">
                    {trend.data.map((point, index) => {
                      const maxValue = Math.max(...trend.data.map(d => d.value))
                      const height = maxValue > 0 ? (point.value / maxValue) * 100 : 0

                      return (
                        <div
                          key={index}
                          className={`flex-1 ${metricConfig.color.replace('text-', 'bg-')} opacity-60 rounded-sm`}
                          style={{ height: `${height}%` }}
                        />
                      )
                    })}
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      ) : (
        // 空状态
        <div className="text-center py-12">
          <BarChart3 className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">还没有健康数据</h3>
          <p className="text-gray-600 mb-6">开始记录您的健康指标，追踪健康趋势</p>
          <button onClick={() => setView('add')} className="btn-primary">
            <Plus className="w-5 h-5 mr-2" />
            添加第一条记录
          </button>
        </div>
      )}

      {/* 最近记录 */}
      {healthMetrics.length > 0 && (
        <div className="bg-white rounded-2xl border border-gray-200 p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-gray-900">最近记录</h3>
            <button
              onClick={() => setView('detail')}
              className="text-blue-600 hover:text-blue-700 text-sm"
            >
              查看全部
            </button>
          </div>

          <div className="space-y-3">
            {arrayUtils
              .sortBy(healthMetrics, 'date', 'desc')
              .slice(0, 5)
              .map(metric => {
                const metricConfig = metricTypes.find(t => t.type === metric.type)!
                const Icon = metricConfig.icon
                const status = getMetricStatus(metric)

                return (
                  <div key={metric.id} className="flex items-center space-x-3">
                    <div
                      className={`w-8 h-8 ${metricConfig.bgColor} rounded-lg flex items-center justify-center`}
                    >
                      <Icon className={`w-4 h-4 ${metricConfig.color}`} />
                    </div>

                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-gray-900">
                          {metricConfig.name}
                        </span>
                        {status !== 'normal' && (
                          <AlertCircle
                            className={`w-4 h-4 ${
                              status === 'high' ? 'text-red-500' : 'text-yellow-500'
                            }`}
                          />
                        )}
                      </div>
                      <p className="text-xs text-gray-600">
                        {dateUtils.format(metric.date, 'MM-DD HH:mm')}
                      </p>
                    </div>

                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">
                        {metric.value} {metricConfig.unit}
                      </p>
                    </div>
                  </div>
                )
              })}
          </div>
        </div>
      )}

      {/* 健康建议 */}
      <div className="bg-blue-50 rounded-2xl p-4 border border-blue-100">
        <div className="flex items-start space-x-3">
          <Target className="w-6 h-6 text-blue-600 mt-1" />
          <div>
            <h4 className="font-medium text-blue-900 mb-2">健康提醒</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• 建议每天固定时间测量血压和体重</li>
              <li>• 保持规律的运动和充足的睡眠</li>
              <li>• 如发现异常指标，请及时咨询医生</li>
            </ul>
          </div>
        </div>
      </div>
    </motion.div>
  )

  // 渲染添加数据表单
  const renderAddForm = () => {
    const [formData, setFormData] = useState({
      type: 'blood_pressure' as HealthMetric['type'],
      value: '',
      date: dateUtils.format(new Date(), 'YYYY-MM-DDTHH:mm'),
      note: '',
    })

    const selectedConfig = metricTypes.find(t => t.type === formData.type)!

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()

      if (!formData.value.trim()) return

      addHealthMetric({
        type: formData.type,
        name: selectedConfig.name,
        value: formData.value,
        unit: selectedConfig.unit,
        date: formData.date,
        note: formData.note,
        normalRange: selectedConfig.normalRange,
      })

      setView('overview')
    }

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <h2 className="text-xl font-bold text-gray-900 mb-2">添加健康数据</h2>
          <p className="text-gray-600">记录您的健康指标</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 指标类型选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">选择指标类型</label>
            <div className="grid grid-cols-2 gap-3">
              {metricTypes.map(metricType => {
                const Icon = metricType.icon
                return (
                  <button
                    key={metricType.type}
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, type: metricType.type }))}
                    className={`flex items-center space-x-3 p-4 rounded-xl border transition-colors ${
                      formData.type === metricType.type
                        ? `${metricType.bgColor} border-current ${metricType.color}`
                        : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <Icon className="w-6 h-6" />
                    <span className="text-sm font-medium">{metricType.name}</span>
                  </button>
                )
              })}
            </div>
          </div>

          {/* 数值输入 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {selectedConfig.name}值
            </label>
            <div className="relative">
              <input
                type="text"
                value={formData.value}
                onChange={e => setFormData(prev => ({ ...prev, value: e.target.value }))}
                placeholder={selectedConfig.placeholder}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
              <span className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 text-sm">
                {selectedConfig.unit}
              </span>
            </div>
            {selectedConfig.normalRange && (
              <p className="text-xs text-gray-500 mt-1">
                正常范围：{selectedConfig.normalRange.min} - {selectedConfig.normalRange.max}{' '}
                {selectedConfig.unit}
              </p>
            )}
          </div>

          {/* 日期时间 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">测量时间</label>
            <input
              type="datetime-local"
              value={formData.date}
              onChange={e => setFormData(prev => ({ ...prev, date: e.target.value }))}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>

          {/* 备注 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">备注（可选）</label>
            <textarea
              value={formData.note}
              onChange={e => setFormData(prev => ({ ...prev, note: e.target.value }))}
              placeholder="记录测量环境、身体状态等..."
              rows={3}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* 提交按钮 */}
          <div className="flex space-x-4">
            <button
              type="button"
              onClick={() => setView('overview')}
              className="flex-1 btn-secondary"
            >
              取消
            </button>
            <button type="submit" className="flex-1 btn-primary">
              <CheckCircle className="w-5 h-5 mr-2" />
              保存记录
            </button>
          </div>
        </form>
      </motion.div>
    )
  }

  return (
    <div className="space-y-6 pt-6">
      <AnimatePresence mode="wait">
        {view === 'overview' && renderOverview()}
        {view === 'add' && renderAddForm()}
        {view === 'detail' && (
          <motion.div
            key="detail"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-gray-900">全部记录</h2>
              <button
                onClick={() => setView('overview')}
                className="text-blue-600 hover:text-blue-700"
              >
                返回概览
              </button>
            </div>

            {healthMetrics.length > 0 ? (
              <div className="space-y-3">
                {arrayUtils.sortBy(healthMetrics, 'date', 'desc').map(metric => {
                  const metricConfig = metricTypes.find(t => t.type === metric.type)!
                  const Icon = metricConfig.icon
                  const status = getMetricStatus(metric)

                  return (
                    <div key={metric.id} className="bg-white rounded-xl border border-gray-200 p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div
                            className={`w-10 h-10 ${metricConfig.bgColor} rounded-xl flex items-center justify-center`}
                          >
                            <Icon className={`w-6 h-6 ${metricConfig.color}`} />
                          </div>
                          <div>
                            <div className="flex items-center space-x-2">
                              <h3 className="font-medium text-gray-900">{metricConfig.name}</h3>
                              {status !== 'normal' && (
                                <AlertCircle
                                  className={`w-4 h-4 ${
                                    status === 'high' ? 'text-red-500' : 'text-yellow-500'
                                  }`}
                                />
                              )}
                            </div>
                            <p className="text-sm text-gray-600">
                              {dateUtils.format(metric.date, 'YYYY-MM-DD HH:mm')}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center space-x-3">
                          <div className="text-right">
                            <p className="font-semibold text-gray-900">
                              {metric.value} {metric.unit}
                            </p>
                            {metric.note && (
                              <p className="text-xs text-gray-500 max-w-20 truncate">
                                {metric.note}
                              </p>
                            )}
                          </div>

                          <button
                            onClick={() => deleteHealthMetric(metric.id)}
                            className="p-2 text-gray-400 hover:text-red-500 transition-colors"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            ) : (
              <div className="text-center py-12">
                <Calendar className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-600">暂无记录数据</p>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default TrendAnalysis
