/**
 * 应用配置文件
 * 统一管理所有配置项，支持环境变量覆盖
 */

interface DifyConfig {
  baseUrl: string
  apiKey: string
  // 报告解读智能体配置
  reportAgent: {
    appId: string
    model: string
  }
  // 全科医生智能体配置
  doctorAgent: {
    appId: string
    model: string
  }
}

interface AppConfig {
  // 应用基础信息
  app: {
    name: string
    version: string
    description: string
  }
  // API配置
  api: {
    timeout: number
    retryCount: number
  }
  // Dify平台配置
  dify: DifyConfig
  // 文件上传配置
  upload: {
    maxSize: number // MB
    allowedTypes: string[]
    maxFiles: number
  }
  // 界面配置
  ui: {
    pageSize: number
    animationDuration: number
  }
}

// 获取Dify基础URL - 开发环境使用代理，生产环境使用直接URL
const getDifyBaseUrl = (): string => {
  return import.meta.env.DEV ? '/api/v1' : import.meta.env.VITE_DIFY_BASE_URL
}

// 默认配置
const defaultConfig: AppConfig = {
  app: {
    name: '个人健康助理',
    version: '1.0.0',
    description: '智能解读体检报告，专业健康咨询',
  },
  api: {
    timeout: 30000, // 30秒超时
    retryCount: 3,
  },
  dify: {
    baseUrl: getDifyBaseUrl(),
    apiKey: import.meta.env.VITE_DIFY_API_KEY,
    reportAgent: {
      appId: import.meta.env.VITE_DIFY_REPORT_APP_ID || 'report-agent-id',
      model: 'gpt-4o',
    },
    doctorAgent: {
      appId: import.meta.env.VITE_DIFY_DOCTOR_APP_ID || 'doctor-agent-id',
      model: 'gpt-4o',
    },
  },
  upload: {
    maxSize: 10, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'],
    maxFiles: 5,
  },
  ui: {
    pageSize: 20,
    animationDuration: 300,
  },
}

// 配置验证函数
const validateConfig = (config: AppConfig): boolean => {
  try {
    // 检查必要的配置项
    if (!config.dify.baseUrl) {
      console.warn('⚠️ Dify Base URL 未配置，某些功能可能无法正常使用')
      return false
    }

    if (!config.dify.apiKey) {
      console.warn('⚠️ Dify API Key 未配置，AI功能将无法使用')
      return false
    }

    console.log('✅ 配置验证通过')
    return true
  } catch (error) {
    console.error('❌ 配置验证失败:', error)
    return false
  }
}

// 配置初始化
const initConfig = (): AppConfig => {
  console.log('📋 正在初始化应用配置...')

  const config = { ...defaultConfig }

  // 验证配置
  validateConfig(config)

  // 开发环境下输出配置信息（隐藏敏感信息）
  if (import.meta.env.DEV) {
    console.log('🔧 当前配置:', {
      ...config,
      dify: {
        ...config.dify,
        apiKey: config.dify.apiKey ? '***已配置***' : '***未配置***',
      },
    })
  }

  return config
}

// 导出配置
export const config = initConfig()

// 导出类型
export type { AppConfig, DifyConfig }

// 配置工具函数
export const configUtils = {
  // 检查是否为开发环境
  isDevelopment: () => import.meta.env.DEV,

  // 检查是否为生产环境
  isProduction: () => import.meta.env.PROD,

  // 获取环境变量
  getEnv: (key: string, defaultValue?: string) => {
    return import.meta.env[key] || defaultValue
  },

  // 检查Dify是否配置完整
  isDifyConfigured: () => Boolean(config.dify.baseUrl && config.dify.apiKey),

  // 检查特定智能体是否配置
  isAgentConfigured: (agentType: 'report' | 'doctor') => {
    const agent = agentType === 'report' ? config.dify.reportAgent : config.dify.doctorAgent
    return Boolean(agent.appId)
  },
}
